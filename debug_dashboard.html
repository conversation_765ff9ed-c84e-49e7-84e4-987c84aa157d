<!DOCTYPE html>
<html>
<head>
    <title>Debug Dashboard API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; background: #2a2a2a; border-radius: 8px; }
        .button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .output { background: #000; padding: 10px; border-radius: 4px; margin: 10px 0; white-space: pre-wrap; font-family: monospace; }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug Dashboard API</h1>
        
        <div class="section">
            <h3>API Tests</h3>
            <button class="button" onclick="testStreamsAPI()">Test /api/streams</button>
            <button class="button" onclick="testStreamsStatusAPI()">Test /api/streams/status</button>
            <button class="button" onclick="testCreateStream()">Test Create Stream</button>
            <div id="apiOutput" class="output"></div>
        </div>
        
        <div class="section">
            <h3>Dashboard Functions Test</h3>
            <button class="button" onclick="testDisplayFunctions()">Test Display Functions</button>
            <button class="button" onclick="testSelectors()">Test CSS Selectors</button>
            <div id="displayOutput" class="output"></div>
        </div>
        
        <div class="section">
            <h3>Mock Data Test</h3>
            <button class="button" onclick="testWithMockData()">Test with Mock Streams</button>
            <div id="mockOutput" class="output"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('apiOutput');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        function logDisplay(message, type = 'info') {
            const output = document.getElementById('displayOutput');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        function logMock(message, type = 'info') {
            const output = document.getElementById('mockOutput');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }

        async function testStreamsAPI() {
            log('Testing /api/streams...');
            try {
                const response = await fetch('/api/streams');
                const data = await response.json();
                log(`Response status: ${response.status}`, response.ok ? 'success' : 'error');
                log(`Response data: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        async function testStreamsStatusAPI() {
            log('Testing /api/streams/status...');
            try {
                const response = await fetch('/api/streams/status');
                const data = await response.json();
                log(`Response status: ${response.status}`, response.ok ? 'success' : 'error');
                log(`Response data: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        async function testCreateStream() {
            log('Testing create stream...');
            // This would need actual form data
            log('Create stream test requires form data - check manually');
        }

        function testDisplayFunctions() {
            logDisplay('Testing display functions...');
            
            // Test if functions exist
            if (typeof displayStreams === 'function') {
                logDisplay('displayStreams function exists', 'success');
            } else {
                logDisplay('displayStreams function NOT found', 'error');
            }
            
            if (typeof updateStreamCounters === 'function') {
                logDisplay('updateStreamCounters function exists', 'success');
            } else {
                logDisplay('updateStreamCounters function NOT found', 'error');
            }
        }

        function testSelectors() {
            logDisplay('Testing CSS selectors...');
            
            // Test mobile container
            const mobileContainer = document.querySelector('.block.md\\:hidden .space-y-4') || 
                                   document.querySelector('.block.md\\:hidden') ||
                                   document.querySelector('[class*="md:hidden"]');
            if (mobileContainer) {
                logDisplay('Mobile container found', 'success');
                logDisplay(`Mobile container classes: ${mobileContainer.className}`);
            } else {
                logDisplay('Mobile container NOT found', 'error');
            }
            
            // Test desktop table
            const tableBody = document.querySelector('.hidden.md\\:block table tbody') ||
                             document.querySelector('[class*="md:block"] table tbody') ||
                             document.querySelector('table tbody');
            if (tableBody) {
                logDisplay('Desktop table body found', 'success');
                logDisplay(`Table body parent classes: ${tableBody.parentElement.parentElement.className}`);
            } else {
                logDisplay('Desktop table body NOT found', 'error');
            }
            
            // Test empty state
            const emptyState = document.getElementById('empty-state');
            if (emptyState) {
                logDisplay('Empty state element found', 'success');
            } else {
                logDisplay('Empty state element NOT found', 'error');
            }
        }

        function testWithMockData() {
            logMock('Testing with mock data...');
            
            const mockStreams = [
                {
                    id: 'test-1',
                    title: 'Test Stream 1',
                    status: 'live',
                    platform: 'YouTube',
                    platform_icon: 'ti-brand-youtube',
                    start_time: new Date().toISOString(),
                    video_thumbnail: 'https://via.placeholder.com/320x180?text=Test+1',
                    resolution: '1280x720',
                    bitrate: 2500,
                    fps: 30,
                    use_advanced_settings: false
                },
                {
                    id: 'test-2',
                    title: 'Test Stream 2',
                    status: 'offline',
                    platform: 'Facebook',
                    platform_icon: 'ti-brand-facebook',
                    video_thumbnail: 'https://via.placeholder.com/320x180?text=Test+2',
                    resolution: '1920x1080',
                    bitrate: 4000,
                    fps: 60,
                    use_advanced_settings: true
                }
            ];
            
            logMock(`Created ${mockStreams.length} mock streams`);
            
            // Try to call display functions if they exist
            if (typeof displayStreams === 'function') {
                try {
                    displayStreams(mockStreams);
                    logMock('displayStreams called successfully', 'success');
                } catch (error) {
                    logMock(`Error calling displayStreams: ${error.message}`, 'error');
                }
            }
            
            if (typeof updateStreamCounters === 'function') {
                try {
                    updateStreamCounters(mockStreams);
                    logMock('updateStreamCounters called successfully', 'success');
                } catch (error) {
                    logMock(`Error calling updateStreamCounters: ${error.message}`, 'error');
                }
            }
        }

        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testStreamsAPI();
                testStreamsStatusAPI();
            }, 1000);
        });
    </script>
</body>
</html>
