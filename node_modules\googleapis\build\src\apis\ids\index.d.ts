/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { ids_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof ids_v1.Ids;
};
export declare function ids(version: 'v1'): ids_v1.Ids;
export declare function ids(options: ids_v1.Options): ids_v1.Ids;
declare const auth: AuthPlus;
export { auth };
export { ids_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
