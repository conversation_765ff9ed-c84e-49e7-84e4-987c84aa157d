/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace policysimulator_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Policy Simulator API
     *
     *  Policy Simulator is a collection of endpoints for creating, running, and viewing a Replay. A `Replay` is a type of simulation that lets you see how your members&#39; access to resources might change if you changed your IAM policy. During a `Replay`, Policy Simulator re-evaluates, or replays, past access attempts under both the current policy and your proposed policy, and compares those results to determine how your members&#39; access might change under the proposed policy.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const policysimulator = google.policysimulator('v1');
     * ```
     */
    export class Policysimulator {
        context: APIRequestContext;
        folders: Resource$Folders;
        operations: Resource$Operations;
        organizations: Resource$Organizations;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Similar to PolicySpec but with an extra 'launch' field for launch reference. The PolicySpec here is specific for dry-run.
     */
    export interface Schema$GoogleCloudOrgpolicyV2AlternatePolicySpec {
        /**
         * Reference to the launch that will be used while audit logging and to control the launch. Should be set only in the alternate policy.
         */
        launch?: string | null;
        /**
         * Specify constraint for configurations of Google Cloud resources.
         */
        spec?: Schema$GoogleCloudOrgpolicyV2PolicySpec;
    }
    /**
     * A custom constraint defined by customers which can *only* be applied to the given resource types and organization. By creating a custom constraint, customers can apply policies of this custom constraint. *Creating a custom constraint itself does NOT apply any policy enforcement*.
     */
    export interface Schema$GoogleCloudOrgpolicyV2CustomConstraint {
        /**
         * Allow or deny type.
         */
        actionType?: string | null;
        /**
         * A Common Expression Language (CEL) condition which is used in the evaluation of the constraint. For example: `resource.instanceName.matches("[production|test]_.*_(\d)+")` or, `resource.management.auto_upgrade == true` The max length of the condition is 1000 characters.
         */
        condition?: string | null;
        /**
         * Detailed information about this custom policy constraint. The max length of the description is 2000 characters.
         */
        description?: string | null;
        /**
         * One line display name for the UI. The max length of the display_name is 200 characters.
         */
        displayName?: string | null;
        /**
         * All the operations being applied for this constraint.
         */
        methodTypes?: string[] | null;
        /**
         * Immutable. Name of the constraint. This is unique within the organization. Format of the name should be * `organizations/{organization_id\}/customConstraints/{custom_constraint_id\}` Example: `organizations/123/customConstraints/custom.createOnlyE2TypeVms` The max length is 70 characters and the minimum length is 1. Note that the prefix `organizations/{organization_id\}/customConstraints/` is not counted.
         */
        name?: string | null;
        /**
         * Immutable. The resource instance type on which this policy applies. Format will be of the form : `/` Example: * `compute.googleapis.com/Instance`.
         */
        resourceTypes?: string[] | null;
        /**
         * Output only. The last time this custom constraint was updated. This represents the last time that the `CreateCustomConstraint` or `UpdateCustomConstraint` methods were called.
         */
        updateTime?: string | null;
    }
    /**
     * Defines an organization policy which is used to specify constraints for configurations of Google Cloud resources.
     */
    export interface Schema$GoogleCloudOrgpolicyV2Policy {
        /**
         * Deprecated.
         */
        alternate?: Schema$GoogleCloudOrgpolicyV2AlternatePolicySpec;
        /**
         * Dry-run policy. Audit-only policy, can be used to monitor how the policy would have impacted the existing and future resources if it's enforced.
         */
        dryRunSpec?: Schema$GoogleCloudOrgpolicyV2PolicySpec;
        /**
         * Optional. An opaque tag indicating the current state of the policy, used for concurrency control. This 'etag' is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding.
         */
        etag?: string | null;
        /**
         * Immutable. The resource name of the policy. Must be one of the following forms, where `constraint_name` is the name of the constraint which this policy configures: * `projects/{project_number\}/policies/{constraint_name\}` * `folders/{folder_id\}/policies/{constraint_name\}` * `organizations/{organization_id\}/policies/{constraint_name\}` For example, `projects/123/policies/compute.disableSerialPortAccess`. Note: `projects/{project_id\}/policies/{constraint_name\}` is also an acceptable name for API requests, but responses will return the name using the equivalent project number.
         */
        name?: string | null;
        /**
         * Basic information about the organization policy.
         */
        spec?: Schema$GoogleCloudOrgpolicyV2PolicySpec;
    }
    /**
     * Defines a Google Cloud policy specification which is used to specify constraints for configurations of Google Cloud resources.
     */
    export interface Schema$GoogleCloudOrgpolicyV2PolicySpec {
        /**
         * An opaque tag indicating the current version of the policySpec, used for concurrency control. This field is ignored if used in a `CreatePolicy` request. When the policy is returned from either a `GetPolicy` or a `ListPolicies` request, this `etag` indicates the version of the current policySpec to use when executing a read-modify-write loop. When the policy is returned from a `GetEffectivePolicy` request, the `etag` will be unset.
         */
        etag?: string | null;
        /**
         * Determines the inheritance behavior for this policy. If `inherit_from_parent` is true, policy rules set higher up in the hierarchy (up to the closest root) are inherited and present in the effective policy. If it is false, then no rules are inherited, and this policy becomes the new root for evaluation. This field can be set only for policies which configure list constraints.
         */
        inheritFromParent?: boolean | null;
        /**
         * Ignores policies set above this resource and restores the `constraint_default` enforcement behavior of the specific constraint at this resource. This field can be set in policies for either list or boolean constraints. If set, `rules` must be empty and `inherit_from_parent` must be set to false.
         */
        reset?: boolean | null;
        /**
         * In policies for boolean constraints, the following requirements apply: - There must be one and only one policy rule where condition is unset. - Boolean policy rules with conditions must set `enforced` to the opposite of the policy rule without a condition. - During policy evaluation, policy rules with conditions that are true for a target resource take precedence.
         */
        rules?: Schema$GoogleCloudOrgpolicyV2PolicySpecPolicyRule[];
        /**
         * Output only. The time stamp this was previously updated. This represents the last time a call to `CreatePolicy` or `UpdatePolicy` was made for that policy.
         */
        updateTime?: string | null;
    }
    /**
     * A rule used to express this policy.
     */
    export interface Schema$GoogleCloudOrgpolicyV2PolicySpecPolicyRule {
        /**
         * Setting this to true means that all values are allowed. This field can be set only in policies for list constraints.
         */
        allowAll?: boolean | null;
        /**
         * A condition which determines whether this rule is used in the evaluation of the policy. When set, the `expression` field in the `Expr' must include from 1 to 10 subexpressions, joined by the "||" or "&&" operators. Each subexpression must be of the form "resource.matchTag('/tag_key_short_name, 'tag_value_short_name')". or "resource.matchTagId('tagKeys/key_id', 'tagValues/value_id')". where key_name and value_name are the resource names for Label Keys and Values. These names are available from the Tag Manager Service. An example expression is: "resource.matchTag('123456789/environment, 'prod')". or "resource.matchTagId('tagKeys/123', 'tagValues/456')".
         */
        condition?: Schema$GoogleTypeExpr;
        /**
         * Setting this to true means that all values are denied. This field can be set only in policies for list constraints.
         */
        denyAll?: boolean | null;
        /**
         * If `true`, then the policy is enforced. If `false`, then any configuration is acceptable. This field can be set only in policies for boolean constraints.
         */
        enforce?: boolean | null;
        /**
         * Optional. Required for managed constraints if parameters are defined. Passes parameter values when policy enforcement is enabled. Ensure that parameter value types match those defined in the constraint definition. For example: { "allowedLocations" : ["us-east1", "us-west1"], "allowAll" : true \}
         */
        parameters?: {
            [key: string]: any;
        } | null;
        /**
         * List of values to be used for this policy rule. This field can be set only in policies for list constraints.
         */
        values?: Schema$GoogleCloudOrgpolicyV2PolicySpecPolicyRuleStringValues;
    }
    /**
     * A message that holds specific allowed and denied values. This message can define specific values and subtrees of the Resource Manager resource hierarchy (`Organizations`, `Folders`, `Projects`) that are allowed or denied. This is achieved by using the `under:` and optional `is:` prefixes. The `under:` prefix is used to denote resource subtree values. The `is:` prefix is used to denote specific values, and is required only if the value contains a ":". Values prefixed with "is:" are treated the same as values with no prefix. Ancestry subtrees must be in one of the following formats: - `projects/` (for example, `projects/tokyo-rain-123`) - `folders/` (for example, `folders/1234`) - `organizations/` (for example, `organizations/1234`) The `supports_under` field of the associated `Constraint` defines whether ancestry prefixes can be used.
     */
    export interface Schema$GoogleCloudOrgpolicyV2PolicySpecPolicyRuleStringValues {
        /**
         * List of values allowed at this resource.
         */
        allowedValues?: string[] | null;
        /**
         * List of values denied at this resource.
         */
        deniedValues?: string[] | null;
    }
    /**
     * A summary and comparison of the principal's access under the current (baseline) policies and the proposed (simulated) policies for a single access tuple.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1AccessStateDiff {
        /**
         * How the principal's access, specified in the AccessState field, changed between the current (baseline) policies and proposed (simulated) policies.
         */
        accessChange?: string | null;
        /**
         * The results of evaluating the access tuple under the current (baseline) policies. If the AccessState couldn't be fully evaluated, this field explains why.
         */
        baseline?: Schema$GoogleCloudPolicysimulatorV1ExplainedAccess;
        /**
         * The results of evaluating the access tuple under the proposed (simulated) policies. If the AccessState couldn't be fully evaluated, this field explains why.
         */
        simulated?: Schema$GoogleCloudPolicysimulatorV1ExplainedAccess;
    }
    /**
     * Information about the principal, resource, and permission to check.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1AccessTuple {
        /**
         * Required. The full resource name that identifies the resource. For example, `//compute.googleapis.com/projects/my-project/zones/us-central1-a/instances/my-instance`. For examples of full resource names for Google Cloud services, see https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
         */
        fullResourceName?: string | null;
        /**
         * Required. The IAM permission to check for the specified principal and resource. For a complete list of IAM permissions, see https://cloud.google.com/iam/help/permissions/reference. For a complete list of predefined IAM roles and the permissions in each role, see https://cloud.google.com/iam/help/roles/reference.
         */
        permission?: string | null;
        /**
         * Required. The principal whose access you want to check, in the form of the email address that represents that principal. For example, `<EMAIL>` or `<EMAIL>`. The principal must be a Google Account or a service account. Other types of principals are not supported.
         */
        principal?: string | null;
    }
    /**
     * CreateOrgPolicyViolationsPreviewOperationMetadata is metadata about an OrgPolicyViolationsPreview generations operation.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1alphaCreateOrgPolicyViolationsPreviewOperationMetadata {
        /**
         * Time when the request was received.
         */
        requestTime?: string | null;
        /**
         * Total number of resources that need scanning. Should equal resource_scanned + resources_pending
         */
        resourcesFound?: number | null;
        /**
         * Number of resources still to scan.
         */
        resourcesPending?: number | null;
        /**
         * Number of resources already scanned.
         */
        resourcesScanned?: number | null;
        /**
         * Time when the request started processing, i.e., when the state was set to RUNNING.
         */
        startTime?: string | null;
        /**
         * Output only. The current state of the operation.
         */
        state?: string | null;
    }
    /**
     * GenerateOrgPolicyViolationsPreviewOperationMetadata is metadata about an OrgPolicyViolationsPreview generations operation.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1alphaGenerateOrgPolicyViolationsPreviewOperationMetadata {
        /**
         * Time when the request was received.
         */
        requestTime?: string | null;
        /**
         * Total number of resources that need scanning. Should equal resource_scanned + resources_pending
         */
        resourcesFound?: number | null;
        /**
         * Number of resources still to scan.
         */
        resourcesPending?: number | null;
        /**
         * Number of resources already scanned.
         */
        resourcesScanned?: number | null;
        /**
         * Time when the request started processing, i.e. when the state was set to RUNNING.
         */
        startTime?: string | null;
        /**
         * The current state of the operation.
         */
        state?: string | null;
    }
    /**
     * The proposed changes to OrgPolicy.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlay {
        /**
         * Optional. The OrgPolicy CustomConstraint changes to preview violations for. Any existing CustomConstraints with the same name will be overridden in the simulation. That is, violations will be determined as if all custom constraints in the overlay were instantiated. Only a single custom_constraint is supported in the overlay at a time. For evaluating multiple constraints, multiple `GenerateOrgPolicyViolationsPreview` requests are made, where each request evaluates a single constraint.
         */
        customConstraints?: Schema$GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlayCustomConstraintOverlay[];
        /**
         * Optional. The OrgPolicy changes to preview violations for. Any existing OrgPolicies with the same name will be overridden in the simulation. That is, violations will be determined as if all policies in the overlay were created or updated.
         */
        policies?: Schema$GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlayPolicyOverlay[];
    }
    /**
     * A change to an OrgPolicy custom constraint.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlayCustomConstraintOverlay {
        /**
         * Optional. The new or updated custom constraint.
         */
        customConstraint?: Schema$GoogleCloudOrgpolicyV2CustomConstraint;
        /**
         * Optional. Resource the constraint is attached to. Example: "organization/987654"
         */
        customConstraintParent?: string | null;
    }
    /**
     * A change to an OrgPolicy.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlayPolicyOverlay {
        /**
         * Optional. The new or updated OrgPolicy.
         */
        policy?: Schema$GoogleCloudOrgpolicyV2Policy;
        /**
         * Optional. The parent of the policy we are attaching to. Example: "projects/123456"
         */
        policyParent?: string | null;
    }
    /**
     * OrgPolicyViolationsPreview is a resource providing a preview of the violations that will exist if an OrgPolicy change is made. The list of violations are modeled as child resources and retrieved via a ListOrgPolicyViolations API call. There are potentially more OrgPolicyViolations than could fit in an embedded field. Thus, the use of a child resource instead of a field.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreview {
        /**
         * Output only. Time when this `OrgPolicyViolationsPreview` was created.
         */
        createTime?: string | null;
        /**
         * Output only. The names of the constraints against which all `OrgPolicyViolations` were evaluated. If `OrgPolicyOverlay` only contains `PolicyOverlay` then it contains the name of the configured custom constraint, applicable to the specified policies. Otherwise it contains the name of the constraint specified in `CustomConstraintOverlay`. Format: `organizations/{organization_id\}/customConstraints/{custom_constraint_id\}` Example: `organizations/123/customConstraints/custom.createOnlyE2TypeVms`
         */
        customConstraints?: string[] | null;
        /**
         * Output only. The resource name of the `OrgPolicyViolationsPreview`. It has the following format: `organizations/{organization\}/locations/{location\}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreview\}` Example: `organizations/my-example-org/locations/global/orgPolicyViolationsPreviews/506a5f7f`
         */
        name?: string | null;
        /**
         * Required. The proposed changes we are previewing violations for.
         */
        overlay?: Schema$GoogleCloudPolicysimulatorV1alphaOrgPolicyOverlay;
        /**
         * Output only. A summary of the state of all resources scanned for compliance with the changed OrgPolicy.
         */
        resourceCounts?: Schema$GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreviewResourceCounts;
        /**
         * Output only. The state of the `OrgPolicyViolationsPreview`.
         */
        state?: string | null;
        /**
         * Output only. The number of OrgPolicyViolations in this `OrgPolicyViolationsPreview`. This count may differ from `resource_summary.noncompliant_count` because each OrgPolicyViolation is specific to a resource **and** constraint. If there are multiple constraints being evaluated (i.e. multiple policies in the overlay), a single resource may violate multiple constraints.
         */
        violationsCount?: number | null;
    }
    /**
     * A summary of the state of all resources scanned for compliance with the changed OrgPolicy.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1alphaOrgPolicyViolationsPreviewResourceCounts {
        /**
         * Output only. Number of scanned resources with zero violations.
         */
        compliant?: number | null;
        /**
         * Output only. Number of resources that returned an error when scanned.
         */
        errors?: number | null;
        /**
         * Output only. Number of scanned resources with at least one violation.
         */
        noncompliant?: number | null;
        /**
         * Output only. Number of resources checked for compliance. Must equal: unenforced + noncompliant + compliant + error
         */
        scanned?: number | null;
        /**
         * Output only. Number of resources where the constraint was not enforced, i.e. the Policy set `enforced: false` for that resource.
         */
        unenforced?: number | null;
    }
    /**
     * CreateOrgPolicyViolationsPreviewOperationMetadata is metadata about an OrgPolicyViolationsPreview generations operation.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1betaCreateOrgPolicyViolationsPreviewOperationMetadata {
        /**
         * Time when the request was received.
         */
        requestTime?: string | null;
        /**
         * Total number of resources that need scanning. Should equal resource_scanned + resources_pending
         */
        resourcesFound?: number | null;
        /**
         * Number of resources still to scan.
         */
        resourcesPending?: number | null;
        /**
         * Number of resources already scanned.
         */
        resourcesScanned?: number | null;
        /**
         * Time when the request started processing, i.e., when the state was set to RUNNING.
         */
        startTime?: string | null;
        /**
         * Output only. The current state of the operation.
         */
        state?: string | null;
    }
    /**
     * GenerateOrgPolicyViolationsPreviewOperationMetadata is metadata about an OrgPolicyViolationsPreview generations operation.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1betaGenerateOrgPolicyViolationsPreviewOperationMetadata {
        /**
         * Time when the request was received.
         */
        requestTime?: string | null;
        /**
         * Total number of resources that need scanning. Should equal resource_scanned + resources_pending
         */
        resourcesFound?: number | null;
        /**
         * Number of resources still to scan.
         */
        resourcesPending?: number | null;
        /**
         * Number of resources already scanned.
         */
        resourcesScanned?: number | null;
        /**
         * Time when the request started processing, i.e. when the state was set to RUNNING.
         */
        startTime?: string | null;
        /**
         * The current state of the operation.
         */
        state?: string | null;
    }
    /**
     * The proposed changes to OrgPolicy.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1betaOrgPolicyOverlay {
        /**
         * Optional. The OrgPolicy CustomConstraint changes to preview violations for. Any existing CustomConstraints with the same name will be overridden in the simulation. That is, violations will be determined as if all custom constraints in the overlay were instantiated. Only a single custom_constraint is supported in the overlay at a time. For evaluating multiple constraints, multiple `GenerateOrgPolicyViolationsPreview` requests are made, where each request evaluates a single constraint.
         */
        customConstraints?: Schema$GoogleCloudPolicysimulatorV1betaOrgPolicyOverlayCustomConstraintOverlay[];
        /**
         * Optional. The OrgPolicy changes to preview violations for. Any existing OrgPolicies with the same name will be overridden in the simulation. That is, violations will be determined as if all policies in the overlay were created or updated.
         */
        policies?: Schema$GoogleCloudPolicysimulatorV1betaOrgPolicyOverlayPolicyOverlay[];
    }
    /**
     * A change to an OrgPolicy custom constraint.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1betaOrgPolicyOverlayCustomConstraintOverlay {
        /**
         * Optional. The new or updated custom constraint.
         */
        customConstraint?: Schema$GoogleCloudOrgpolicyV2CustomConstraint;
        /**
         * Optional. Resource the constraint is attached to. Example: "organization/987654"
         */
        customConstraintParent?: string | null;
    }
    /**
     * A change to an OrgPolicy.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1betaOrgPolicyOverlayPolicyOverlay {
        /**
         * Optional. The new or updated OrgPolicy.
         */
        policy?: Schema$GoogleCloudOrgpolicyV2Policy;
        /**
         * Optional. The parent of the policy we are attaching to. Example: "projects/123456"
         */
        policyParent?: string | null;
    }
    /**
     * OrgPolicyViolationsPreview is a resource providing a preview of the violations that will exist if an OrgPolicy change is made. The list of violations are modeled as child resources and retrieved via a ListOrgPolicyViolations API call. There are potentially more OrgPolicyViolations than could fit in an embedded field. Thus, the use of a child resource instead of a field.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1betaOrgPolicyViolationsPreview {
        /**
         * Output only. Time when this `OrgPolicyViolationsPreview` was created.
         */
        createTime?: string | null;
        /**
         * Output only. The names of the constraints against which all `OrgPolicyViolations` were evaluated. If `OrgPolicyOverlay` only contains `PolicyOverlay` then it contains the name of the configured custom constraint, applicable to the specified policies. Otherwise it contains the name of the constraint specified in `CustomConstraintOverlay`. Format: `organizations/{organization_id\}/customConstraints/{custom_constraint_id\}` Example: `organizations/123/customConstraints/custom.createOnlyE2TypeVms`
         */
        customConstraints?: string[] | null;
        /**
         * Output only. The resource name of the `OrgPolicyViolationsPreview`. It has the following format: `organizations/{organization\}/locations/{location\}/orgPolicyViolationsPreviews/{orgPolicyViolationsPreview\}` Example: `organizations/my-example-org/locations/global/orgPolicyViolationsPreviews/506a5f7f`
         */
        name?: string | null;
        /**
         * Required. The proposed changes we are previewing violations for.
         */
        overlay?: Schema$GoogleCloudPolicysimulatorV1betaOrgPolicyOverlay;
        /**
         * Output only. A summary of the state of all resources scanned for compliance with the changed OrgPolicy.
         */
        resourceCounts?: Schema$GoogleCloudPolicysimulatorV1betaOrgPolicyViolationsPreviewResourceCounts;
        /**
         * Output only. The state of the `OrgPolicyViolationsPreview`.
         */
        state?: string | null;
        /**
         * Output only. The number of OrgPolicyViolations in this `OrgPolicyViolationsPreview`. This count may differ from `resource_summary.noncompliant_count` because each OrgPolicyViolation is specific to a resource **and** constraint. If there are multiple constraints being evaluated (i.e. multiple policies in the overlay), a single resource may violate multiple constraints.
         */
        violationsCount?: number | null;
    }
    /**
     * A summary of the state of all resources scanned for compliance with the changed OrgPolicy.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1betaOrgPolicyViolationsPreviewResourceCounts {
        /**
         * Output only. Number of scanned resources with zero violations.
         */
        compliant?: number | null;
        /**
         * Output only. Number of resources that returned an error when scanned.
         */
        errors?: number | null;
        /**
         * Output only. Number of scanned resources with at least one violation.
         */
        noncompliant?: number | null;
        /**
         * Output only. Number of resources checked for compliance. Must equal: unenforced + noncompliant + compliant + error
         */
        scanned?: number | null;
        /**
         * Output only. Number of resources where the constraint was not enforced, i.e. the Policy set `enforced: false` for that resource.
         */
        unenforced?: number | null;
    }
    /**
     * Details about how a binding in a policy affects a principal's ability to use a permission.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1BindingExplanation {
        /**
         * Required. Indicates whether _this binding_ provides the specified permission to the specified principal for the specified resource. This field does _not_ indicate whether the principal actually has the permission for the resource. There might be another binding that overrides this binding. To determine whether the principal actually has the permission, use the `access` field in the TroubleshootIamPolicyResponse.
         */
        access?: string | null;
        /**
         * A condition expression that prevents this binding from granting access unless the expression evaluates to `true`. To learn about IAM Conditions, see https://cloud.google.com/iam/docs/conditions-overview.
         */
        condition?: Schema$GoogleTypeExpr;
        /**
         * Indicates whether each principal in the binding includes the principal specified in the request, either directly or indirectly. Each key identifies a principal in the binding, and each value indicates whether the principal in the binding includes the principal in the request. For example, suppose that a binding includes the following principals: * `user:<EMAIL>` * `group:<EMAIL>` The principal in the replayed access tuple is `user:<EMAIL>`. This user is a principal of the group `group:<EMAIL>`. For the first principal in the binding, the key is `user:<EMAIL>`, and the `membership` field in the value is set to `MEMBERSHIP_NOT_INCLUDED`. For the second principal in the binding, the key is `group:<EMAIL>`, and the `membership` field in the value is set to `MEMBERSHIP_INCLUDED`.
         */
        memberships?: {
            [key: string]: Schema$GoogleCloudPolicysimulatorV1BindingExplanationAnnotatedMembership;
        } | null;
        /**
         * The relevance of this binding to the overall determination for the entire policy.
         */
        relevance?: string | null;
        /**
         * The role that this binding grants. For example, `roles/compute.serviceAgent`. For a complete list of predefined IAM roles, as well as the permissions in each role, see https://cloud.google.com/iam/help/roles/reference.
         */
        role?: string | null;
        /**
         * Indicates whether the role granted by this binding contains the specified permission.
         */
        rolePermission?: string | null;
        /**
         * The relevance of the permission's existence, or nonexistence, in the role to the overall determination for the entire policy.
         */
        rolePermissionRelevance?: string | null;
    }
    /**
     * Details about whether the binding includes the principal.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1BindingExplanationAnnotatedMembership {
        /**
         * Indicates whether the binding includes the principal.
         */
        membership?: string | null;
        /**
         * The relevance of the principal's status to the overall determination for the binding.
         */
        relevance?: string | null;
    }
    /**
     * Details about how a set of policies, listed in ExplainedPolicy, resulted in a certain AccessState when replaying an access tuple.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1ExplainedAccess {
        /**
         * Whether the principal in the access tuple has permission to access the resource in the access tuple under the given policies.
         */
        accessState?: string | null;
        /**
         * If the AccessState is `UNKNOWN`, this field contains a list of errors explaining why the result is `UNKNOWN`. If the `AccessState` is `GRANTED` or `NOT_GRANTED`, this field is omitted.
         */
        errors?: Schema$GoogleRpcStatus[];
        /**
         * If the AccessState is `UNKNOWN`, this field contains the policies that led to that result. If the `AccessState` is `GRANTED` or `NOT_GRANTED`, this field is omitted.
         */
        policies?: Schema$GoogleCloudPolicysimulatorV1ExplainedPolicy[];
    }
    /**
     * Details about how a specific IAM Policy contributed to the access check.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1ExplainedPolicy {
        /**
         * Indicates whether _this policy_ provides the specified permission to the specified principal for the specified resource. This field does _not_ indicate whether the principal actually has the permission for the resource. There might be another policy that overrides this policy. To determine whether the principal actually has the permission, use the `access` field in the TroubleshootIamPolicyResponse.
         */
        access?: string | null;
        /**
         * Details about how each binding in the policy affects the principal's ability, or inability, to use the permission for the resource. If the user who created the Replay does not have access to the policy, this field is omitted.
         */
        bindingExplanations?: Schema$GoogleCloudPolicysimulatorV1BindingExplanation[];
        /**
         * The full resource name that identifies the resource. For example, `//compute.googleapis.com/projects/my-project/zones/us-central1-a/instances/my-instance`. If the user who created the Replay does not have access to the policy, this field is omitted. For examples of full resource names for Google Cloud services, see https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
         */
        fullResourceName?: string | null;
        /**
         * The IAM policy attached to the resource. If the user who created the Replay does not have access to the policy, this field is empty.
         */
        policy?: Schema$GoogleIamV1Policy;
        /**
         * The relevance of this policy to the overall determination in the TroubleshootIamPolicyResponse. If the user who created the Replay does not have access to the policy, this field is omitted.
         */
        relevance?: string | null;
    }
    /**
     * Response message for Simulator.ListReplayResults.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse {
        /**
         * A token that you can use to retrieve the next page of ReplayResult objects. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The results of running a Replay.
         */
        replayResults?: Schema$GoogleCloudPolicysimulatorV1ReplayResult[];
    }
    /**
     * A resource describing a `Replay`, or simulation.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1Replay {
        /**
         * Required. The configuration used for the `Replay`.
         */
        config?: Schema$GoogleCloudPolicysimulatorV1ReplayConfig;
        /**
         * Output only. The resource name of the `Replay`, which has the following format: `{projects|folders|organizations\}/{resource-id\}/locations/global/replays/{replay-id\}`, where `{resource-id\}` is the ID of the project, folder, or organization that owns the Replay. Example: `projects/my-example-project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
         */
        name?: string | null;
        /**
         * Output only. Summary statistics about the replayed log entries.
         */
        resultsSummary?: Schema$GoogleCloudPolicysimulatorV1ReplayResultsSummary;
        /**
         * Output only. The current state of the `Replay`.
         */
        state?: string | null;
    }
    /**
     * The configuration used for a Replay.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1ReplayConfig {
        /**
         * The logs to use as input for the Replay.
         */
        logSource?: string | null;
        /**
         * A mapping of the resources that you want to simulate policies for and the policies that you want to simulate. Keys are the full resource names for the resources. For example, `//cloudresourcemanager.googleapis.com/projects/my-project`. For examples of full resource names for Google Cloud services, see https://cloud.google.com/iam/help/troubleshooter/full-resource-names. Values are Policy objects representing the policies that you want to simulate. Replays automatically take into account any IAM policies inherited through the resource hierarchy, and any policies set on descendant resources. You do not need to include these policies in the policy overlay.
         */
        policyOverlay?: {
            [key: string]: Schema$GoogleIamV1Policy;
        } | null;
    }
    /**
     * The difference between the results of evaluating an access tuple under the current (baseline) policies and under the proposed (simulated) policies. This difference explains how a principal's access could change if the proposed policies were applied.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1ReplayDiff {
        /**
         * A summary and comparison of the principal's access under the current (baseline) policies and the proposed (simulated) policies for a single access tuple. The evaluation of the principal's access is reported in the AccessState field.
         */
        accessDiff?: Schema$GoogleCloudPolicysimulatorV1AccessStateDiff;
    }
    /**
     * Metadata about a Replay operation.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1ReplayOperationMetadata {
        /**
         * Time when the request was received.
         */
        startTime?: string | null;
    }
    /**
     * The result of replaying a single access tuple against a simulated state.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1ReplayResult {
        /**
         * The access tuple that was replayed. This field includes information about the principal, resource, and permission that were involved in the access attempt.
         */
        accessTuple?: Schema$GoogleCloudPolicysimulatorV1AccessTuple;
        /**
         * The difference between the principal's access under the current (baseline) policies and the principal's access under the proposed (simulated) policies. This field is only included for access tuples that were successfully replayed and had different results under the current policies and the proposed policies.
         */
        diff?: Schema$GoogleCloudPolicysimulatorV1ReplayDiff;
        /**
         * The error that caused the access tuple replay to fail. This field is only included for access tuples that were not replayed successfully.
         */
        error?: Schema$GoogleRpcStatus;
        /**
         * The latest date this access tuple was seen in the logs.
         */
        lastSeenDate?: Schema$GoogleTypeDate;
        /**
         * The resource name of the `ReplayResult`, in the following format: `{projects|folders|organizations\}/{resource-id\}/locations/global/replays/{replay-id\}/results/{replay-result-id\}`, where `{resource-id\}` is the ID of the project, folder, or organization that owns the Replay. Example: `projects/my-example-project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36/results/1234`
         */
        name?: string | null;
        /**
         * The Replay that the access tuple was included in.
         */
        parent?: string | null;
    }
    /**
     * Summary statistics about the replayed log entries.
     */
    export interface Schema$GoogleCloudPolicysimulatorV1ReplayResultsSummary {
        /**
         * The number of replayed log entries with a difference between baseline and simulated policies.
         */
        differenceCount?: number | null;
        /**
         * The number of log entries that could not be replayed.
         */
        errorCount?: number | null;
        /**
         * The total number of log entries replayed.
         */
        logCount?: number | null;
        /**
         * The date of the newest log entry replayed.
         */
        newestDate?: Schema$GoogleTypeDate;
        /**
         * The date of the oldest log entry replayed.
         */
        oldestDate?: Schema$GoogleTypeDate;
        /**
         * The number of replayed log entries with no difference between baseline and simulated policies.
         */
        unchangedCount?: number | null;
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$GoogleIamV1AuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$GoogleIamV1AuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$GoogleIamV1AuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$GoogleIamV1Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$GoogleTypeExpr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/group/{group_id\}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/x`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/group/{group_id\}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/x`: All identities in a workload identity pool. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).
         */
        role?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$GoogleIamV1Policy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$GoogleIamV1AuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$GoogleIamV1Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$GoogleLongrunningListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$GoogleLongrunningOperation[];
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$GoogleLongrunningOperation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$GoogleRpcStatus;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$GoogleRpcStatus {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp
     */
    export interface Schema$GoogleTypeDate {
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        day?: number | null;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        month?: number | null;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        year?: number | null;
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$GoogleTypeExpr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    export class Resource$Folders {
        context: APIRequestContext;
        locations: Resource$Folders$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Folders$Locations {
        context: APIRequestContext;
        orgPolicyViolationsPreviews: Resource$Folders$Locations$Orgpolicyviolationspreviews;
        replays: Resource$Folders$Locations$Replays;
        constructor(context: APIRequestContext);
    }
    export class Resource$Folders$Locations$Orgpolicyviolationspreviews {
        context: APIRequestContext;
        operations: Resource$Folders$Locations$Orgpolicyviolationspreviews$Operations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Folders$Locations$Orgpolicyviolationspreviews$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Folders$Locations$Orgpolicyviolationspreviews$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Folders$Locations$Orgpolicyviolationspreviews$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        get(params: Params$Resource$Folders$Locations$Orgpolicyviolationspreviews$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Folders$Locations$Orgpolicyviolationspreviews$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Folders$Locations$Orgpolicyviolationspreviews$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
    }
    export interface Params$Resource$Folders$Locations$Orgpolicyviolationspreviews$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export class Resource$Folders$Locations$Replays {
        context: APIRequestContext;
        operations: Resource$Folders$Locations$Replays$Operations;
        results: Resource$Folders$Locations$Replays$Results;
        constructor(context: APIRequestContext);
        /**
         * Creates and starts a Replay using the given ReplayConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Folders$Locations$Replays$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Folders$Locations$Replays$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        create(params: Params$Resource$Folders$Locations$Replays$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Folders$Locations$Replays$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Folders$Locations$Replays$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets the specified Replay. Each `Replay` is available for at least 7 days.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Folders$Locations$Replays$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Folders$Locations$Replays$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudPolicysimulatorV1Replay>;
        get(params: Params$Resource$Folders$Locations$Replays$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Folders$Locations$Replays$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>): void;
        get(params: Params$Resource$Folders$Locations$Replays$Get, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>): void;
    }
    export interface Params$Resource$Folders$Locations$Replays$Create extends StandardParameters {
        /**
         * Required. The parent resource where this Replay will be created. This resource must be a project, folder, or organization with a location. Example: `projects/my-example-project/locations/global`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudPolicysimulatorV1Replay;
    }
    export interface Params$Resource$Folders$Locations$Replays$Get extends StandardParameters {
        /**
         * Required. The name of the Replay to retrieve, in the following format: `{projects|folders|organizations\}/{resource-id\}/locations/global/replays/{replay-id\}`, where `{resource-id\}` is the ID of the project, folder, or organization that owns the `Replay`. Example: `projects/my-example-project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
         */
        name?: string;
    }
    export class Resource$Folders$Locations$Replays$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Folders$Locations$Replays$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Folders$Locations$Replays$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        get(params: Params$Resource$Folders$Locations$Replays$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Folders$Locations$Replays$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Folders$Locations$Replays$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Folders$Locations$Replays$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Folders$Locations$Replays$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningListOperationsResponse>;
        list(params: Params$Resource$Folders$Locations$Replays$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Folders$Locations$Replays$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(params: Params$Resource$Folders$Locations$Replays$Operations$List, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
    }
    export interface Params$Resource$Folders$Locations$Replays$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Folders$Locations$Replays$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Folders$Locations$Replays$Results {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists the results of running a Replay.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Folders$Locations$Replays$Results$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Folders$Locations$Replays$Results$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>;
        list(params: Params$Resource$Folders$Locations$Replays$Results$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Folders$Locations$Replays$Results$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>): void;
        list(params: Params$Resource$Folders$Locations$Replays$Results$List, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>): void;
    }
    export interface Params$Resource$Folders$Locations$Replays$Results$List extends StandardParameters {
        /**
         * The maximum number of ReplayResult objects to return. Defaults to 5000. The maximum value is 5000; values above 5000 are rounded down to 5000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous Simulator.ListReplayResults call. Provide this token to retrieve the next page of results. When paginating, all other parameters provided to [Simulator.ListReplayResults[] must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The Replay whose results are listed, in the following format: `{projects|folders|organizations\}/{resource-id\}/locations/global/replays/{replay-id\}` Example: `projects/my-project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
         */
        parent?: string;
    }
    export class Resource$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningListOperationsResponse>;
        list(params: Params$Resource$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(params: Params$Resource$Operations$List, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
    }
    export interface Params$Resource$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Organizations {
        context: APIRequestContext;
        locations: Resource$Organizations$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Organizations$Locations {
        context: APIRequestContext;
        orgPolicyViolationsPreviews: Resource$Organizations$Locations$Orgpolicyviolationspreviews;
        replays: Resource$Organizations$Locations$Replays;
        constructor(context: APIRequestContext);
    }
    export class Resource$Organizations$Locations$Orgpolicyviolationspreviews {
        context: APIRequestContext;
        operations: Resource$Organizations$Locations$Orgpolicyviolationspreviews$Operations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Organizations$Locations$Orgpolicyviolationspreviews$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Orgpolicyviolationspreviews$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Organizations$Locations$Orgpolicyviolationspreviews$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        get(params: Params$Resource$Organizations$Locations$Orgpolicyviolationspreviews$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Orgpolicyviolationspreviews$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Organizations$Locations$Orgpolicyviolationspreviews$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
    }
    export interface Params$Resource$Organizations$Locations$Orgpolicyviolationspreviews$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export class Resource$Organizations$Locations$Replays {
        context: APIRequestContext;
        operations: Resource$Organizations$Locations$Replays$Operations;
        results: Resource$Organizations$Locations$Replays$Results;
        constructor(context: APIRequestContext);
        /**
         * Creates and starts a Replay using the given ReplayConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Organizations$Locations$Replays$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Organizations$Locations$Replays$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        create(params: Params$Resource$Organizations$Locations$Replays$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Organizations$Locations$Replays$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Organizations$Locations$Replays$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets the specified Replay. Each `Replay` is available for at least 7 days.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Replays$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Organizations$Locations$Replays$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudPolicysimulatorV1Replay>;
        get(params: Params$Resource$Organizations$Locations$Replays$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Replays$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>): void;
        get(params: Params$Resource$Organizations$Locations$Replays$Get, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>): void;
    }
    export interface Params$Resource$Organizations$Locations$Replays$Create extends StandardParameters {
        /**
         * Required. The parent resource where this Replay will be created. This resource must be a project, folder, or organization with a location. Example: `projects/my-example-project/locations/global`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudPolicysimulatorV1Replay;
    }
    export interface Params$Resource$Organizations$Locations$Replays$Get extends StandardParameters {
        /**
         * Required. The name of the Replay to retrieve, in the following format: `{projects|folders|organizations\}/{resource-id\}/locations/global/replays/{replay-id\}`, where `{resource-id\}` is the ID of the project, folder, or organization that owns the `Replay`. Example: `projects/my-example-project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
         */
        name?: string;
    }
    export class Resource$Organizations$Locations$Replays$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Organizations$Locations$Replays$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Organizations$Locations$Replays$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        get(params: Params$Resource$Organizations$Locations$Replays$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Organizations$Locations$Replays$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Organizations$Locations$Replays$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Replays$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Organizations$Locations$Replays$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningListOperationsResponse>;
        list(params: Params$Resource$Organizations$Locations$Replays$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Replays$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Replays$Operations$List, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
    }
    export interface Params$Resource$Organizations$Locations$Replays$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Organizations$Locations$Replays$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Organizations$Locations$Replays$Results {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists the results of running a Replay.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Organizations$Locations$Replays$Results$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Organizations$Locations$Replays$Results$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>;
        list(params: Params$Resource$Organizations$Locations$Replays$Results$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Organizations$Locations$Replays$Results$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>): void;
        list(params: Params$Resource$Organizations$Locations$Replays$Results$List, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>): void;
    }
    export interface Params$Resource$Organizations$Locations$Replays$Results$List extends StandardParameters {
        /**
         * The maximum number of ReplayResult objects to return. Defaults to 5000. The maximum value is 5000; values above 5000 are rounded down to 5000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous Simulator.ListReplayResults call. Provide this token to retrieve the next page of results. When paginating, all other parameters provided to [Simulator.ListReplayResults[] must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The Replay whose results are listed, in the following format: `{projects|folders|organizations\}/{resource-id\}/locations/global/replays/{replay-id\}` Example: `projects/my-project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
         */
        parent?: string;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        orgPolicyViolationsPreviews: Resource$Projects$Locations$Orgpolicyviolationspreviews;
        replays: Resource$Projects$Locations$Replays;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations$Orgpolicyviolationspreviews {
        context: APIRequestContext;
        operations: Resource$Projects$Locations$Orgpolicyviolationspreviews$Operations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations$Orgpolicyviolationspreviews$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Orgpolicyviolationspreviews$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Orgpolicyviolationspreviews$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        get(params: Params$Resource$Projects$Locations$Orgpolicyviolationspreviews$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Orgpolicyviolationspreviews$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Projects$Locations$Orgpolicyviolationspreviews$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
    }
    export interface Params$Resource$Projects$Locations$Orgpolicyviolationspreviews$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export class Resource$Projects$Locations$Replays {
        context: APIRequestContext;
        operations: Resource$Projects$Locations$Replays$Operations;
        results: Resource$Projects$Locations$Replays$Results;
        constructor(context: APIRequestContext);
        /**
         * Creates and starts a Replay using the given ReplayConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Replays$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Replays$Create, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        create(params: Params$Resource$Projects$Locations$Replays$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Replays$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(params: Params$Resource$Projects$Locations$Replays$Create, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        create(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Gets the specified Replay. Each `Replay` is available for at least 7 days.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Replays$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Replays$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudPolicysimulatorV1Replay>;
        get(params: Params$Resource$Projects$Locations$Replays$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Replays$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>): void;
        get(params: Params$Resource$Projects$Locations$Replays$Get, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>): void;
        get(callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1Replay>): void;
    }
    export interface Params$Resource$Projects$Locations$Replays$Create extends StandardParameters {
        /**
         * Required. The parent resource where this Replay will be created. This resource must be a project, folder, or organization with a location. Example: `projects/my-example-project/locations/global`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleCloudPolicysimulatorV1Replay;
    }
    export interface Params$Resource$Projects$Locations$Replays$Get extends StandardParameters {
        /**
         * Required. The name of the Replay to retrieve, in the following format: `{projects|folders|organizations\}/{resource-id\}/locations/global/replays/{replay-id\}`, where `{resource-id\}` is the ID of the project, folder, or organization that owns the `Replay`. Example: `projects/my-example-project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
         */
        name?: string;
    }
    export class Resource$Projects$Locations$Replays$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Replays$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Replays$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningOperation>;
        get(params: Params$Resource$Projects$Locations$Replays$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Replays$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Projects$Locations$Replays$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Replays$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Replays$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleLongrunningListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Replays$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Replays$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Replays$Operations$List, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Replays$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Replays$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Replays$Results {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists the results of running a Replay.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Replays$Results$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Replays$Results$List, options?: MethodOptions): GaxiosPromise<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>;
        list(params: Params$Resource$Projects$Locations$Replays$Results$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Replays$Results$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Replays$Results$List, callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleCloudPolicysimulatorV1ListReplayResultsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Replays$Results$List extends StandardParameters {
        /**
         * The maximum number of ReplayResult objects to return. Defaults to 5000. The maximum value is 5000; values above 5000 are rounded down to 5000.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous Simulator.ListReplayResults call. Provide this token to retrieve the next page of results. When paginating, all other parameters provided to [Simulator.ListReplayResults[] must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The Replay whose results are listed, in the following format: `{projects|folders|organizations\}/{resource-id\}/locations/global/replays/{replay-id\}` Example: `projects/my-project/locations/global/replays/506a5f7f-38ce-4d7d-8e03-479ce1833c36`
         */
        parent?: string;
    }
    export {};
}
