const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connect to database
const dbPath = path.join(__dirname, '..', 'streamflow.db');
const db = new sqlite3.Database(dbPath);

console.log('Adding duration column to streams table...');

// Check if column exists first
db.all('PRAGMA table_info(streams)', [], (err, rows) => {
  if (err) {
    console.error('Error getting table info:', err);
    db.close();
    return;
  }

  console.log('Current streams table columns:');
  rows.forEach(col => {
    console.log(`  - ${col.name}: ${col.type}`);
  });

  // Check if duration column exists
  const durationExists = rows.some(col => col.name === 'duration');
  
  if (durationExists) {
    console.log('✓ Duration column already exists');
    db.close();
    return;
  }

  console.log('\nAdding duration column...');
  
  // Add duration column
  db.run('ALTER TABLE streams ADD COLUMN duration INTEGER', function(err) {
    if (err) {
      console.error('❌ Error adding duration column:', err.message);
    } else {
      console.log('✓ Duration column added successfully');
    }

    // Show final table structure
    db.all('PRAGMA table_info(streams)', [], (err, finalRows) => {
      if (err) {
        console.error('Error getting final table info:', err);
      } else {
        console.log('\nFinal streams table columns:');
        finalRows.forEach(col => {
          console.log(`  - ${col.name}: ${col.type}`);
        });
      }
      
      db.close();
      console.log('✅ Done!');
    });
  });
});
