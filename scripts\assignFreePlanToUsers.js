const { db } = require('../db/database');

async function assignFreePlanToUsers() {
  console.log('Assigning Free plan to users without plans...');
  
  try {
    // Find users with no plan or "none" plan
    const usersWithoutPlan = await new Promise((resolve, reject) => {
      db.all(`
        SELECT id, username, plan_type, max_streaming_slots, max_storage_gb
        FROM users 
        WHERE plan_type IS NULL OR plan_type = 'none' OR plan_type = ''
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (usersWithoutPlan.length === 0) {
      console.log('✓ All users already have plans assigned');
      return;
    }

    console.log(`Found ${usersWithoutPlan.length} users without plans:`);
    
    for (const user of usersWithoutPlan) {
      console.log(`- ${user.username} (ID: ${user.id}) - Current plan: ${user.plan_type || 'none'}`);
      
      // Update user to Free plan with default limits
      await new Promise((resolve, reject) => {
        db.run(`
          UPDATE users 
          SET plan_type = 'Free', 
              max_streaming_slots = 1, 
              max_storage_gb = 5,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `, [user.id], function(err) {
          if (err) reject(err);
          else {
            console.log(`  ✓ Assigned Free plan to ${user.username}`);
            resolve();
          }
        });
      });
    }
    
    console.log('✓ Successfully assigned Free plan to all users');
    
    // Show updated user plans
    const updatedUsers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT username, plan_type, max_streaming_slots, max_storage_gb 
        FROM users 
        ORDER BY username ASC
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('\nCurrent user plans:');
    updatedUsers.forEach(user => {
      console.log(`- ${user.username}: ${user.plan_type} (${user.max_streaming_slots} slots, ${user.max_storage_gb}GB storage)`);
    });
    
  } catch (error) {
    console.error('Error assigning Free plans:', error);
  }
}

// Run if this script is executed directly
if (require.main === module) {
  assignFreePlanToUsers().then(() => {
    console.log('Script completed');
    process.exit(0);
  }).catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

module.exports = { assignFreePlanToUsers };
