const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connect to database
const dbPath = path.join(__dirname, '..', 'streamflow.db');
const db = new sqlite3.Database(dbPath);

async function cleanDuplicatePlans() {
  console.log('Cleaning duplicate plans...');
  
  try {
    // Get all plans including inactive ones
    const allPlans = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans ORDER BY name, created_at', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`Found ${allPlans.length} total plans`);

    // Group plans by name
    const plansByName = {};
    allPlans.forEach(plan => {
      const name = plan.name.toLowerCase().trim();
      if (!plansByName[name]) {
        plansByName[name] = [];
      }
      plansByName[name].push(plan);
    });

    // Find duplicates
    const duplicates = [];
    Object.keys(plansByName).forEach(name => {
      if (plansByName[name].length > 1) {
        duplicates.push({
          name: name,
          plans: plansByName[name]
        });
      }
    });

    if (duplicates.length === 0) {
      console.log('✓ No duplicate plans found');
      return;
    }

    console.log(`Found ${duplicates.length} sets of duplicate plans:`);
    
    for (const duplicate of duplicates) {
      console.log(`\n📋 Plans with name "${duplicate.name}":`);
      duplicate.plans.forEach((plan, index) => {
        console.log(`  ${index + 1}. ID: ${plan.id} | Active: ${plan.is_active} | Created: ${plan.created_at}`);
      });

      // Keep the most recent active plan, or the most recent one if none are active
      const activePlans = duplicate.plans.filter(p => p.is_active === 1);
      let planToKeep;
      
      if (activePlans.length > 0) {
        // Keep the most recent active plan
        planToKeep = activePlans.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0];
      } else {
        // Keep the most recent plan overall
        planToKeep = duplicate.plans.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0];
      }

      console.log(`  ✓ Keeping plan ID: ${planToKeep.id} (${planToKeep.is_active ? 'active' : 'inactive'})`);

      // Mark other plans as inactive
      const plansToDeactivate = duplicate.plans.filter(p => p.id !== planToKeep.id);
      
      for (const plan of plansToDeactivate) {
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE subscription_plans SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [plan.id],
            function(err) {
              if (err) {
                console.error(`  ❌ Error deactivating plan ${plan.id}:`, err.message);
                reject(err);
              } else {
                console.log(`  ✓ Deactivated duplicate plan ID: ${plan.id}`);
                resolve();
              }
            }
          );
        });
      }
    }

    // Show final active plans
    console.log('\n📊 Final active plans:');
    const finalPlans = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY name', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    finalPlans.forEach(plan => {
      console.log(`  - ${plan.name}: $${plan.price} (${plan.max_streaming_slots} slots, ${plan.max_storage_gb}GB)`);
    });

    console.log('\n✅ Duplicate plan cleanup completed!');
    
  } catch (error) {
    console.error('❌ Error cleaning duplicate plans:', error);
  } finally {
    db.close();
  }
}

// Run cleanup if this script is executed directly
if (require.main === module) {
  cleanDuplicatePlans().then(() => {
    console.log('Cleanup script finished');
    process.exit(0);
  }).catch(error => {
    console.error('Cleanup script failed:', error);
    process.exit(1);
  });
}

module.exports = { cleanDuplicatePlans };
