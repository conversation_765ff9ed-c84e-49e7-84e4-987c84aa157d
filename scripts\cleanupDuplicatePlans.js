const { db } = require('../db/database');

async function cleanupDuplicatePlans() {
  console.log('Starting cleanup of duplicate subscription plans...');
  
  try {
    // Find all plans with the same name
    const duplicatePlans = await new Promise((resolve, reject) => {
      db.all(`
        SELECT name, COUNT(*) as count, GROUP_CONCAT(id) as ids
        FROM subscription_plans 
        GROUP BY LOWER(name) 
        HAVING count > 1
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (duplicatePlans.length === 0) {
      console.log('✓ No duplicate plans found');
      return;
    }

    console.log(`Found ${duplicatePlans.length} sets of duplicate plans:`);
    
    for (const duplicate of duplicatePlans) {
      console.log(`- ${duplicate.name}: ${duplicate.count} duplicates`);
      
      const planIds = duplicate.ids.split(',');
      
      // Keep the first plan, remove the rest
      const keepId = planIds[0];
      const removeIds = planIds.slice(1);
      
      console.log(`  Keeping plan ID: ${keepId}`);
      console.log(`  Removing plan IDs: ${removeIds.join(', ')}`);
      
      // Remove duplicate plans
      for (const removeId of removeIds) {
        await new Promise((resolve, reject) => {
          db.run('DELETE FROM subscription_plans WHERE id = ?', [removeId], function(err) {
            if (err) reject(err);
            else {
              console.log(`  ✓ Removed duplicate plan ${removeId}`);
              resolve();
            }
          });
        });
      }
    }
    
    console.log('✓ Cleanup completed successfully');
    
    // Show remaining plans
    const remainingPlans = await new Promise((resolve, reject) => {
      db.all('SELECT id, name, price FROM subscription_plans ORDER BY price ASC', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('\nRemaining subscription plans:');
    remainingPlans.forEach(plan => {
      console.log(`- ${plan.name}: $${plan.price} (ID: ${plan.id})`);
    });
    
  } catch (error) {
    console.error('Error during cleanup:', error);
  }
}

// Run cleanup if this script is executed directly
if (require.main === module) {
  cleanupDuplicatePlans().then(() => {
    console.log('Cleanup script finished');
    process.exit(0);
  }).catch(error => {
    console.error('Cleanup script failed:', error);
    process.exit(1);
  });
}

module.exports = { cleanupDuplicatePlans };
