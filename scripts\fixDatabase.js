const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, '..', 'db', 'streamflow.db');
const db = new sqlite3.Database(dbPath);

console.log('Fixing database schema...');

// Function to check if column exists
function columnExists(tableName, columnName) {
  return new Promise((resolve, reject) => {
    db.all(`PRAGMA table_info(${tableName})`, [], (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      const exists = rows.some(row => row.name === columnName);
      resolve(exists);
    });
  });
}

// Function to add column if it doesn't exist
function addColumnIfNotExists(tableName, columnName, columnType) {
  return new Promise(async (resolve, reject) => {
    try {
      const exists = await columnExists(tableName, columnName);
      if (!exists) {
        db.run(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnType}`, (err) => {
          if (err) {
            console.error(`Error adding column ${columnName} to ${tableName}:`, err.message);
            reject(err);
          } else {
            console.log(`✓ Added column ${columnName} to ${tableName}`);
            resolve();
          }
        });
      } else {
        console.log(`✓ Column ${columnName} already exists in ${tableName}`);
        resolve();
      }
    } catch (error) {
      reject(error);
    }
  });
}

// Fix database schema
async function fixDatabase() {
  try {
    console.log('Checking and fixing database schema...');

    // Fix videos table
    await addColumnIfNotExists('videos', 'created_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP');
    await addColumnIfNotExists('videos', 'updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP');

    // Fix users table (add missing columns if any)
    await addColumnIfNotExists('users', 'email', 'TEXT UNIQUE');
    await addColumnIfNotExists('users', 'role', 'TEXT DEFAULT "user"');
    await addColumnIfNotExists('users', 'plan_type', 'TEXT DEFAULT "free"');
    await addColumnIfNotExists('users', 'max_streaming_slots', 'INTEGER DEFAULT 1');
    await addColumnIfNotExists('users', 'max_storage_gb', 'INTEGER DEFAULT 5');
    await addColumnIfNotExists('users', 'used_storage_gb', 'REAL DEFAULT 0');
    await addColumnIfNotExists('users', 'subscription_start_date', 'TIMESTAMP');
    await addColumnIfNotExists('users', 'subscription_end_date', 'TIMESTAMP');
    await addColumnIfNotExists('users', 'is_active', 'BOOLEAN DEFAULT 1');
    await addColumnIfNotExists('users', 'updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP');

    // Fix streams table
    await addColumnIfNotExists('streams', 'user_id', 'TEXT');
    await addColumnIfNotExists('streams', 'updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP');

    // Fix stream_history table
    await addColumnIfNotExists('stream_history', 'user_id', 'TEXT');

    console.log('✅ Database schema fixed successfully!');

    // Update existing records to have proper timestamps
    console.log('Updating existing records...');

    // Update videos without created_at
    db.run(`UPDATE videos SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL`, (err) => {
      if (err) {
        console.error('Error updating videos timestamps:', err.message);
      } else {
        console.log('✓ Updated videos timestamps');
      }
    });

    // Update users without updated_at
    db.run(`UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL`, (err) => {
      if (err) {
        console.error('Error updating users timestamps:', err.message);
      } else {
        console.log('✓ Updated users timestamps');
      }
    });

    // Update streams without user_id (set to first admin user)
    db.get(`SELECT id FROM users WHERE role = 'admin' LIMIT 1`, [], (err, row) => {
      if (err) {
        console.error('Error getting admin user:', err.message);
      } else if (row) {
        db.run(`UPDATE streams SET user_id = ? WHERE user_id IS NULL`, [row.id], (err) => {
          if (err) {
            console.error('Error updating streams user_id:', err.message);
          } else {
            console.log('✓ Updated streams user_id');
          }
        });

        db.run(`UPDATE stream_history SET user_id = ? WHERE user_id IS NULL`, [row.id], (err) => {
          if (err) {
            console.error('Error updating stream_history user_id:', err.message);
          } else {
            console.log('✓ Updated stream_history user_id');
          }
        });
      }
    });

    console.log('✅ Database migration completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing database:', error);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err.message);
      } else {
        console.log('Database connection closed.');
      }
    });
  }
}

// Run the fix
fixDatabase();
