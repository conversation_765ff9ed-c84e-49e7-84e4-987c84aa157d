const { db } = require('../db/database');

async function fixFreePlanPrice() {
  console.log('Fixing Free plan price...');
  
  try {
    await new Promise((resolve, reject) => {
      db.run('UPDATE subscription_plans SET price = 0 WHERE name = ?', ['Free'], function(err) {
        if (err) {
          reject(err);
        } else {
          console.log(`✓ Updated ${this.changes} Free plan(s) to price $0`);
          resolve();
        }
      });
    });
    
    // Verify the change
    const freePlans = await new Promise((resolve, reject) => {
      db.all('SELECT id, name, price FROM subscription_plans WHERE name = ?', ['Free'], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('Free plans after update:');
    freePlans.forEach(plan => {
      console.log(`- ${plan.name}: $${plan.price} (ID: ${plan.id})`);
    });
    
  } catch (error) {
    console.error('Error fixing Free plan price:', error);
  }
}

// Run if this script is executed directly
if (require.main === module) {
  fixFreePlanPrice().then(() => {
    console.log('Fix completed');
    process.exit(0);
  }).catch(error => {
    console.error('Fix failed:', error);
    process.exit(1);
  });
}

module.exports = { fixFreePlanPrice };
