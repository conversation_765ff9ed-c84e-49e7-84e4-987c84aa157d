const { db } = require('../db/database');

async function fixPreviewPlan() {
  console.log('🔧 Fixing Preview plan configuration...\n');

  try {
    // Update Preview plan to have correct storage
    console.log('1. Updating Preview plan storage to 2GB...');
    await new Promise((resolve, reject) => {
      db.run(
        'UPDATE subscription_plans SET max_storage_gb = 2 WHERE name = ?',
        ['Preview'],
        function(err) {
          if (err) reject(err);
          else {
            console.log(`   ✅ Updated ${this.changes} Preview plan(s)`);
            resolve();
          }
        }
      );
    });

    // Update all Preview users to have correct storage
    console.log('\n2. Updating Preview users to have 2GB storage...');
    await new Promise((resolve, reject) => {
      db.run(
        'UPDATE users SET max_storage_gb = 2 WHERE plan_type = ? AND max_storage_gb != 2',
        ['Preview'],
        function(err) {
          if (err) reject(err);
          else {
            console.log(`   ✅ Updated ${this.changes} Preview user(s)`);
            resolve();
          }
        }
      );
    });

    // Verify the changes
    console.log('\n3. Verifying changes...');
    const previewPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = ?', ['Preview'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (previewPlan) {
      console.log(`   Preview plan: ${previewPlan.max_streaming_slots} slots, ${previewPlan.max_storage_gb}GB storage`);
    }

    const previewUsers = await new Promise((resolve, reject) => {
      db.all(
        'SELECT username, max_streaming_slots, max_storage_gb FROM users WHERE plan_type = ?',
        ['Preview'],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    console.log(`   Preview users: ${previewUsers.length}`);
    previewUsers.forEach(user => {
      console.log(`     - ${user.username}: ${user.max_streaming_slots} slots, ${user.max_storage_gb}GB`);
    });

    console.log('\n✅ Preview plan fix completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing Preview plan:', error);
  }
}

// Run the fix if this script is executed directly
if (require.main === module) {
  fixPreviewPlan().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

module.exports = { fixPreviewPlan };
