const { db } = require('../db/database');
const User = require('../models/User');
const Subscription = require('../models/Subscription');

async function fixUserPlans() {
  console.log('🔧 Fixing user plans and subscriptions...\n');

  try {
    // 1. Get all users with Preview plan
    console.log('1. Finding users with Preview plan...');
    const previewUsers = await new Promise((resolve, reject) => {
      db.all(
        'SELECT id, username, plan_type, max_streaming_slots, max_storage_gb FROM users WHERE plan_type = ?',
        ['Preview'],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    console.log(`   Found ${previewUsers.length} users with Preview plan`);

    // 2. Get Preview plan details
    console.log('\n2. Getting Preview plan details...');
    const previewPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = ? AND is_active = 1', ['Preview'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!previewPlan) {
      console.log('   ❌ Preview plan not found in subscription_plans table');
      return;
    }

    console.log(`   ✅ Preview plan found: ${previewPlan.max_streaming_slots} slots, ${previewPlan.max_storage_gb}GB storage`);

    // 3. Fix users with wrong limits
    console.log('\n3. Fixing users with incorrect limits...');
    let fixedCount = 0;
    
    for (const user of previewUsers) {
      const needsUpdate = 
        user.max_streaming_slots !== previewPlan.max_streaming_slots ||
        user.max_storage_gb !== previewPlan.max_storage_gb;

      if (needsUpdate) {
        console.log(`   Fixing ${user.username}: ${user.max_streaming_slots} → ${previewPlan.max_streaming_slots} slots, ${user.max_storage_gb} → ${previewPlan.max_storage_gb}GB`);
        
        await User.updatePlan(
          user.id,
          'Preview',
          previewPlan.max_streaming_slots,
          previewPlan.max_storage_gb
        );
        
        fixedCount++;
      }
    }

    console.log(`   ✅ Fixed ${fixedCount} users with incorrect limits`);

    // 4. Create missing subscriptions for Preview users
    console.log('\n4. Creating missing subscriptions for Preview users...');
    let subscriptionsCreated = 0;

    for (const user of previewUsers) {
      // Check if user already has a subscription
      const existingSubscription = await Subscription.getUserSubscription(user.id);
      
      if (!existingSubscription) {
        console.log(`   Creating subscription for ${user.username}`);
        
        // Create subscription that never expires
        const endDate = new Date();
        endDate.setFullYear(endDate.getFullYear() + 10);
        
        await Subscription.createSubscription({
          user_id: user.id,
          plan_id: previewPlan.id,
          status: 'active',
          end_date: endDate.toISOString(),
          payment_method: 'free'
        });
        
        subscriptionsCreated++;
      }
    }

    console.log(`   ✅ Created ${subscriptionsCreated} missing subscriptions`);

    // 5. Summary
    console.log('\n📊 Summary:');
    console.log(`   - Users with Preview plan: ${previewUsers.length}`);
    console.log(`   - Users with fixed limits: ${fixedCount}`);
    console.log(`   - Subscriptions created: ${subscriptionsCreated}`);
    console.log('\n✅ User plan fixes completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing user plans:', error);
  }
}

// Run the fix if this script is executed directly
if (require.main === module) {
  fixUserPlans().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

module.exports = { fixUserPlans };
