const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const dbPath = path.join(__dirname, '..', 'db', 'streamflow.db');
const db = new sqlite3.Database(dbPath);

console.log('Initializing database...');

// Create tables in sequence
db.serialize(() => {
  // Users table
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE,
    password TEXT NOT NULL,
    avatar_path TEXT,
    gdrive_api_key TEXT,
    role TEXT DEFAULT 'user',
    plan_type TEXT DEFAULT 'Preview',
    max_streaming_slots INTEGER DEFAULT 0,
    max_storage_gb INTEGER DEFAULT 2,
    used_storage_gb REAL DEFAULT 0,
    subscription_start_date TIMESTAMP,
    subscription_end_date TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating users table:', err.message);
    } else {
      console.log('✓ Users table created');
    }
  });

  // Videos table
  db.run(`CREATE TABLE IF NOT EXISTS videos (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    filepath TEXT NOT NULL,
    thumbnail_path TEXT,
    file_size INTEGER,
    duration INTEGER,
    resolution TEXT,
    bitrate INTEGER,
    fps INTEGER,
    codec TEXT,
    format TEXT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating videos table:', err.message);
    } else {
      console.log('✓ Videos table created');
    }
  });

  // Streams table
  db.run(`CREATE TABLE IF NOT EXISTS streams (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    video_id TEXT,
    rtmp_url TEXT NOT NULL,
    stream_key TEXT NOT NULL,
    platform TEXT,
    platform_icon TEXT,
    bitrate INTEGER DEFAULT 2500,
    resolution TEXT,
    fps INTEGER DEFAULT 30,
    orientation TEXT DEFAULT 'horizontal',
    loop_video BOOLEAN DEFAULT 1,
    schedule_time TIMESTAMP,
    duration INTEGER,
    status TEXT DEFAULT 'offline',
    status_updated_at TIMESTAMP,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    use_advanced_settings BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (video_id) REFERENCES videos(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating streams table:', err.message);
    } else {
      console.log('✓ Streams table created');
    }
  });

  // Stream history table
  db.run(`CREATE TABLE IF NOT EXISTS stream_history (
    id TEXT PRIMARY KEY,
    stream_id TEXT,
    title TEXT NOT NULL,
    platform TEXT,
    platform_icon TEXT,
    video_id TEXT,
    video_title TEXT,
    resolution TEXT,
    bitrate INTEGER,
    fps INTEGER,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration INTEGER,
    use_advanced_settings BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (stream_id) REFERENCES streams(id),
    FOREIGN KEY (video_id) REFERENCES videos(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating stream_history table:', err.message);
    } else {
      console.log('✓ Stream history table created');
    }
  });

  // Subscription plans table
  db.run(`CREATE TABLE IF NOT EXISTS subscription_plans (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    price REAL NOT NULL,
    currency TEXT DEFAULT 'USD',
    billing_period TEXT DEFAULT 'monthly',
    max_streaming_slots INTEGER DEFAULT 1,
    max_storage_gb INTEGER DEFAULT 5,
    features TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating subscription_plans table:', err.message);
    } else {
      console.log('✓ Subscription plans table created');
    }
  });

  // User subscriptions table
  db.run(`CREATE TABLE IF NOT EXISTS user_subscriptions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    plan_id TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    payment_method TEXT,
    payment_id TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
  )`, (err) => {
    if (err) {
      console.error('Error creating user_subscriptions table:', err.message);
    } else {
      console.log('✓ User subscriptions table created');
    }
  });

  // Role permissions table
  db.run(`CREATE TABLE IF NOT EXISTS role_permissions (
    id TEXT PRIMARY KEY,
    role TEXT NOT NULL,
    permission TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`, (err) => {
    if (err) {
      console.error('Error creating role_permissions table:', err.message);
    } else {
      console.log('✓ Role permissions table created');
    }
  });

  // Insert default subscription plans
  console.log('Inserting default subscription plans...');
  const defaultPlans = [
    {
      id: uuidv4(),
      name: 'Preview',
      price: 0,
      currency: 'USD',
      billing_period: 'monthly',
      max_streaming_slots: 0,
      max_storage_gb: 2,
      features: JSON.stringify(['0 Streaming Slots', '2GB Storage', 'Preview Only', 'Basic Support'])
    },
    {
      id: uuidv4(),
      name: 'Basic',
      price: 9.99,
      currency: 'USD',
      billing_period: 'monthly',
      max_streaming_slots: 3,
      max_storage_gb: 25,
      features: JSON.stringify(['3 Streaming Slots', '25GB Storage', 'Priority Support', 'HD Streaming'])
    }
  ];

  defaultPlans.forEach(plan => {
    db.run(
      `INSERT OR IGNORE INTO subscription_plans
       (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [plan.id, plan.name, plan.price, plan.currency, plan.billing_period,
       plan.max_streaming_slots, plan.max_storage_gb, plan.features],
      (err) => {
        if (err) {
          console.error('Error inserting plan:', plan.name, err.message);
        } else {
          console.log('✓ Inserted plan:', plan.name);
        }
      }
    );
  });

  // Insert default permissions
  console.log('Inserting default permissions...');
  const defaultPermissions = [
    // Admin permissions
    { role: 'admin', permission: 'manage_users' },
    { role: 'admin', permission: 'manage_plans' },
    { role: 'admin', permission: 'view_all_streams' },
    { role: 'admin', permission: 'manage_system' },
    { role: 'admin', permission: 'unlimited_streaming' },
    { role: 'admin', permission: 'unlimited_storage' },

    // User permissions
    { role: 'user', permission: 'create_stream' },
    { role: 'user', permission: 'upload_video' },
    { role: 'user', permission: 'view_own_streams' },
    { role: 'user', permission: 'manage_profile' },

    // Moderator permissions
    { role: 'moderator', permission: 'view_all_streams' },
    { role: 'moderator', permission: 'moderate_content' },
    { role: 'moderator', permission: 'manage_profile' }
  ];

  defaultPermissions.forEach(perm => {
    db.run(
      `INSERT OR IGNORE INTO role_permissions (id, role, permission) VALUES (?, ?, ?)`,
      [uuidv4(), perm.role, perm.permission],
      (err) => {
        if (err) {
          console.error('Error inserting permission:', perm.role, perm.permission, err.message);
        } else {
          console.log('✓ Inserted permission:', perm.role, '->', perm.permission);
        }
      }
    );
  });

  console.log('Database initialization completed!');
});

db.close((err) => {
  if (err) {
    console.error('Error closing database:', err.message);
  } else {
    console.log('Database connection closed.');
  }
});
