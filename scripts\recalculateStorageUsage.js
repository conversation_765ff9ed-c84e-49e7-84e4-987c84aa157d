const { db } = require('../db/database');
const User = require('../models/User');

async function recalculateStorageUsage() {
  console.log('🔄 Recalculating storage usage for all users...\n');

  try {
    // 1. Get all users
    console.log('1. Getting all users...');
    const users = await new Promise((resolve, reject) => {
      db.all('SELECT id, username, used_storage_gb FROM users', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`   Found ${users.length} users`);

    // 2. Calculate storage for each user
    let totalUpdated = 0;
    let totalErrors = 0;

    for (const user of users) {
      try {
        console.log(`\n2. Processing user: ${user.username} (${user.id})`);
        console.log(`   Current storage: ${user.used_storage_gb || 0}GB`);

        // Get all videos for this user
        const videos = await new Promise((resolve, reject) => {
          db.all(
            'SELECT id, title, file_size FROM videos WHERE user_id = ?',
            [user.id],
            (err, rows) => {
              if (err) reject(err);
              else resolve(rows);
            }
          );
        });

        console.log(`   Found ${videos.length} videos`);

        // Calculate total storage in GB
        let totalStorageBytes = 0;
        videos.forEach(video => {
          const fileSize = video.file_size || 0;
          totalStorageBytes += fileSize;
          console.log(`     - ${video.title}: ${(fileSize / (1024 * 1024 * 1024)).toFixed(3)}GB`);
        });

        const totalStorageGB = totalStorageBytes / (1024 * 1024 * 1024);
        console.log(`   Calculated total: ${totalStorageGB.toFixed(3)}GB`);

        // Update user's storage usage
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE users SET used_storage_gb = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [totalStorageGB, user.id],
            function(err) {
              if (err) reject(err);
              else {
                console.log(`   ✅ Updated storage: ${user.used_storage_gb || 0}GB → ${totalStorageGB.toFixed(3)}GB`);
                resolve();
              }
            }
          );
        });

        totalUpdated++;

      } catch (error) {
        console.error(`   ❌ Error processing user ${user.username}: ${error.message}`);
        totalErrors++;
      }
    }

    // 3. Summary
    console.log('\n3. Summary:');
    console.log(`   ✅ Users updated: ${totalUpdated}`);
    console.log(`   ❌ Errors: ${totalErrors}`);

    // 4. Verify results
    console.log('\n4. Verification - Updated storage usage:');
    const updatedUsers = await new Promise((resolve, reject) => {
      db.all(
        'SELECT username, used_storage_gb, max_storage_gb FROM users ORDER BY used_storage_gb DESC',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    updatedUsers.forEach(user => {
      const percentage = user.max_storage_gb > 0 ? ((user.used_storage_gb / user.max_storage_gb) * 100).toFixed(1) : 0;
      console.log(`   - ${user.username}: ${user.used_storage_gb.toFixed(3)}GB / ${user.max_storage_gb}GB (${percentage}%)`);
    });

    // 5. Check for potential issues
    console.log('\n5. Checking for potential issues:');
    
    // Users over quota
    const overQuotaUsers = updatedUsers.filter(user => user.used_storage_gb > user.max_storage_gb);
    if (overQuotaUsers.length > 0) {
      console.log(`   ⚠️  Users over quota: ${overQuotaUsers.length}`);
      overQuotaUsers.forEach(user => {
        console.log(`     - ${user.username}: ${user.used_storage_gb.toFixed(3)}GB / ${user.max_storage_gb}GB`);
      });
    } else {
      console.log('   ✅ No users over quota');
    }

    // Users with 0 storage but have videos
    const zeroStorageWithVideos = await new Promise((resolve, reject) => {
      db.all(`
        SELECT u.username, u.used_storage_gb, COUNT(v.id) as video_count
        FROM users u
        LEFT JOIN videos v ON u.id = v.user_id
        WHERE u.used_storage_gb = 0 AND v.id IS NOT NULL
        GROUP BY u.id, u.username, u.used_storage_gb
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (zeroStorageWithVideos.length > 0) {
      console.log(`   ⚠️  Users with 0 storage but have videos: ${zeroStorageWithVideos.length}`);
      zeroStorageWithVideos.forEach(user => {
        console.log(`     - ${user.username}: ${user.video_count} videos but 0GB storage`);
      });
    } else {
      console.log('   ✅ No users with 0 storage but have videos');
    }

    console.log('\n✅ Storage recalculation completed successfully!');

  } catch (error) {
    console.error('❌ Storage recalculation failed:', error);
    throw error;
  }
}

// Helper function to check video file sizes on disk
async function verifyVideoFileSizes() {
  console.log('\n🔍 Verifying video file sizes on disk...');
  
  const fs = require('fs');
  const path = require('path');

  try {
    const videos = await new Promise((resolve, reject) => {
      db.all(
        'SELECT id, title, filepath, file_size, user_id FROM videos',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    console.log(`Found ${videos.length} videos in database`);

    let missingFiles = 0;
    let sizeMismatches = 0;
    let correctFiles = 0;

    for (const video of videos) {
      try {
        const fullPath = path.join(__dirname, '..', 'public', video.filepath);
        
        if (!fs.existsSync(fullPath)) {
          console.log(`   ❌ Missing file: ${video.title} (${video.filepath})`);
          missingFiles++;
          continue;
        }

        const stats = fs.statSync(fullPath);
        const actualSize = stats.size;
        const dbSize = video.file_size || 0;

        if (Math.abs(actualSize - dbSize) > 1024) { // Allow 1KB difference
          console.log(`   ⚠️  Size mismatch: ${video.title}`);
          console.log(`      DB: ${(dbSize / (1024 * 1024)).toFixed(2)}MB, Disk: ${(actualSize / (1024 * 1024)).toFixed(2)}MB`);
          sizeMismatches++;
          
          // Update database with correct size
          await new Promise((resolve, reject) => {
            db.run(
              'UPDATE videos SET file_size = ? WHERE id = ?',
              [actualSize, video.id],
              function(err) {
                if (err) reject(err);
                else {
                  console.log(`      ✅ Updated database with correct size`);
                  resolve();
                }
              }
            );
          });
        } else {
          correctFiles++;
        }

      } catch (error) {
        console.error(`   ❌ Error checking ${video.title}: ${error.message}`);
      }
    }

    console.log('\nFile verification summary:');
    console.log(`   ✅ Correct files: ${correctFiles}`);
    console.log(`   ⚠️  Size mismatches (fixed): ${sizeMismatches}`);
    console.log(`   ❌ Missing files: ${missingFiles}`);

    if (sizeMismatches > 0) {
      console.log('\n🔄 Re-running storage calculation after fixing file sizes...');
      await recalculateStorageUsage();
    }

  } catch (error) {
    console.error('❌ File verification failed:', error);
  }
}

// Helper function to clean up orphaned video records
async function cleanupOrphanedVideos() {
  console.log('\n🧹 Cleaning up orphaned video records...');
  
  const fs = require('fs');
  const path = require('path');

  try {
    const videos = await new Promise((resolve, reject) => {
      db.all(
        'SELECT id, title, filepath FROM videos',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    let orphanedRecords = 0;

    for (const video of videos) {
      try {
        const fullPath = path.join(__dirname, '..', 'public', video.filepath);
        
        if (!fs.existsSync(fullPath)) {
          console.log(`   🗑️  Removing orphaned record: ${video.title}`);
          
          await new Promise((resolve, reject) => {
            db.run('DELETE FROM videos WHERE id = ?', [video.id], function(err) {
              if (err) reject(err);
              else {
                orphanedRecords++;
                resolve();
              }
            });
          });
        }

      } catch (error) {
        console.error(`   ❌ Error checking ${video.title}: ${error.message}`);
      }
    }

    console.log(`   ✅ Removed ${orphanedRecords} orphaned video records`);

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
}

// Run script if executed directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const shouldVerifyFiles = args.includes('--verify-files');
  const shouldCleanup = args.includes('--cleanup');

  (async () => {
    try {
      if (shouldCleanup) {
        await cleanupOrphanedVideos();
      }
      
      if (shouldVerifyFiles) {
        await verifyVideoFileSizes();
      } else {
        await recalculateStorageUsage();
      }
      
      console.log('\n🎉 All operations completed successfully!');
      process.exit(0);
    } catch (error) {
      console.error('❌ Script failed:', error);
      process.exit(1);
    }
  })();
}

module.exports = { 
  recalculateStorageUsage, 
  verifyVideoFileSizes, 
  cleanupOrphanedVideos 
};
