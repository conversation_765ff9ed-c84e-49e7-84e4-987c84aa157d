const { db } = require('../db/database');
const Stream = require('../models/Stream');

async function testAutoStopFunctionality() {
  console.log('Testing Auto-Stop Functionality...\n');

  try {
    // 1. Check current streams
    console.log('1. Current streams in database:');
    const allStreams = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM streams ORDER BY created_at DESC LIMIT 10', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    allStreams.forEach(stream => {
      console.log(`  - ${stream.title}: ${stream.status} (RTMP: ${stream.rtmp_url}, Key: ${stream.stream_key})`);
    });

    // 2. Identify problematic streams
    console.log('\n2. Analyzing streams for potential issues:');
    
    const problematicStreams = allStreams.filter(stream => {
      const issues = [];
      
      // Check for placeholder stream keys
      const placeholderKeys = ['123', 'test', 'your_stream_key', 'stream_key'];
      if (placeholderKeys.includes(stream.stream_key)) {
        issues.push('Placeholder stream key');
      }
      
      // Check for invalid RTMP URLs
      if (!stream.rtmp_url || !stream.rtmp_url.startsWith('rtmp')) {
        issues.push('Invalid RTMP URL');
      }
      
      // Check for localhost URLs
      if (stream.rtmp_url && stream.rtmp_url.includes('localhost')) {
        issues.push('Localhost RTMP URL');
      }
      
      if (issues.length > 0) {
        console.log(`    ⚠️  ${stream.title}:`);
        issues.forEach(issue => console.log(`       - ${issue}`));
        return true;
      }
      
      return false;
    });

    // 3. Test auto-stop configuration
    console.log('\n3. Auto-Stop Configuration:');
    console.log('   - Max consecutive failures: 5');
    console.log('   - Failure window: 5 minutes');
    console.log('   - I/O error threshold: 3');
    console.log('   - Connection error threshold: 3');
    console.log('   - Blacklist duration: 15 minutes');

    // 4. Simulate error scenarios
    console.log('\n4. Error scenarios that trigger auto-stop:');
    
    const errorScenarios = [
      {
        error: 'I/O error',
        description: 'Network connectivity issues, invalid RTMP endpoint',
        threshold: 3,
        example: 'rtmp://a.rtmp.youtube.com/live2/123: I/O error'
      },
      {
        error: 'Connection refused',
        description: 'RTMP server rejecting connections',
        threshold: 3,
        example: 'Connection refused'
      },
      {
        error: 'Invalid stream key',
        description: 'Authentication failure due to wrong stream key',
        threshold: 1,
        example: 'Invalid stream key'
      },
      {
        error: 'Authentication failed',
        description: 'Stream key authentication failure',
        threshold: 1,
        example: 'Authentication failed'
      },
      {
        error: '403 Forbidden',
        description: 'Access denied by streaming platform',
        threshold: 1,
        example: '403 Forbidden'
      },
      {
        error: 'FFmpeg exit code 1',
        description: 'General FFmpeg failure (after 3 retries)',
        threshold: 3,
        example: 'FFmpeg process exited with error code 1'
      }
    ];

    errorScenarios.forEach(scenario => {
      console.log(`\n   📋 ${scenario.error}:`);
      console.log(`      Description: ${scenario.description}`);
      console.log(`      Threshold: ${scenario.threshold} occurrences`);
      console.log(`      Example: ${scenario.example}`);
    });

    // 5. Expected behavior
    console.log('\n5. Expected Auto-Stop Behavior:');
    console.log('   ✅ Stream with "123" stream key should auto-stop after 3 I/O errors');
    console.log('   ✅ Stream should be added to blacklist for 15 minutes');
    console.log('   ✅ User should see "Stream auto-stopped" message in logs');
    console.log('   ✅ Stream status should be set to "offline"');
    console.log('   ✅ No more restart attempts should occur');
    console.log('   ✅ User can manually clear failed status via API');

    // 6. Test with actual problematic stream
    if (problematicStreams.length > 0) {
      console.log('\n6. Testing with problematic streams:');
      
      for (const stream of problematicStreams.slice(0, 2)) { // Test max 2 streams
        console.log(`\n   Testing stream: ${stream.title}`);
        console.log(`   RTMP URL: ${stream.rtmp_url}`);
        console.log(`   Stream Key: ${stream.stream_key}`);
        
        // Predict what will happen
        const predictions = [];
        
        if (stream.stream_key === '123') {
          predictions.push('Will fail with I/O error (invalid stream key)');
          predictions.push('Should auto-stop after 3 I/O errors');
        }
        
        if (stream.rtmp_url && stream.rtmp_url.includes('localhost')) {
          predictions.push('Will fail with connection refused');
          predictions.push('Should auto-stop after 3 connection errors');
        }
        
        if (!stream.rtmp_url || !stream.rtmp_url.startsWith('rtmp')) {
          predictions.push('Will fail immediately due to invalid URL');
        }
        
        console.log('   Predictions:');
        predictions.forEach(prediction => {
          console.log(`     - ${prediction}`);
        });
      }
    }

    // 7. Manual testing instructions
    console.log('\n7. Manual Testing Instructions:');
    console.log('   1. Create a stream with RTMP URL: rtmp://a.rtmp.youtube.com/live2');
    console.log('   2. Use stream key: 123 (invalid placeholder)');
    console.log('   3. Start the stream');
    console.log('   4. Watch console logs for:');
    console.log('      - [FFMPEG_STDERR] I/O error messages');
    console.log('      - Auto-stop decision messages');
    console.log('      - Stream auto-stopped messages');
    console.log('   5. Verify stream status becomes "offline"');
    console.log('   6. Try to start again - should be blocked (blacklisted)');
    console.log('   7. Use API to clear failed status');
    console.log('   8. Should be able to start again (after fixing RTMP settings)');

    // 8. API endpoints for testing
    console.log('\n8. API Endpoints for Testing:');
    console.log('   Clear failed status:');
    console.log('   POST /api/streams/{streamId}/clear-failed');
    console.log('   ');
    console.log('   Validate RTMP config:');
    console.log('   POST /api/validate-rtmp');
    console.log('   Body: { "rtmpUrl": "...", "streamKey": "..." }');

    // 9. Monitoring
    console.log('\n9. Monitoring Auto-Stop:');
    console.log('   Watch for these log patterns:');
    console.log('   - [StreamingService] Auto-stopping stream {id}: Too many I/O errors');
    console.log('   - [StreamingService] Stream auto-stopped: {reason}');
    console.log('   - [StreamingService] Stream {id} removed from failed streams list after cooldown');

    console.log('\n✅ Auto-Stop Test Analysis Completed!');
    console.log('\nNext Steps:');
    console.log('1. Start a stream with invalid settings (like stream key "123")');
    console.log('2. Monitor console logs for auto-stop behavior');
    console.log('3. Verify stream stops automatically after repeated failures');
    console.log('4. Test manual recovery using clear-failed API');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Helper function to simulate stream failure tracking
function simulateFailureTracking() {
  console.log('\n🧪 Simulating Failure Tracking:');
  
  const failures = [
    { timestamp: Date.now() - 4000, error: 'I/O error' },
    { timestamp: Date.now() - 3000, error: 'I/O error' },
    { timestamp: Date.now() - 2000, error: 'I/O error' },
    { timestamp: Date.now() - 1000, error: 'I/O error' }
  ];
  
  console.log('Recent failures:');
  failures.forEach((failure, index) => {
    const secondsAgo = Math.floor((Date.now() - failure.timestamp) / 1000);
    console.log(`  ${index + 1}. ${failure.error} (${secondsAgo}s ago)`);
  });
  
  const ioErrors = failures.filter(f => f.error.includes('I/O error')).length;
  console.log(`\nI/O errors count: ${ioErrors}/3 (threshold)`);
  
  if (ioErrors >= 3) {
    console.log('🛑 Would trigger auto-stop: Too many I/O errors');
  } else {
    console.log('✅ Below threshold, would continue retrying');
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testAutoStopFunctionality().then(() => {
    simulateFailureTracking();
    console.log('\nTest script finished');
    process.exit(0);
  }).catch(error => {
    console.error('Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testAutoStopFunctionality };
