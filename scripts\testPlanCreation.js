const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

async function testPlanCreation() {
  console.log('Testing plan creation with 0 values...');
  
  try {
    // Test plan with price 0 and streaming slots 0
    const testPlan = {
      id: uuidv4(),
      name: 'Free Limited Test',
      price: 0,
      currency: 'IDR',
      billing_period: 'monthly',
      max_streaming_slots: 0,
      max_storage_gb: 1,
      features: JSON.stringify(['1GB Storage', 'No Streaming', 'Basic Support'])
    };

    console.log('Creating test plan:', testPlan);

    await new Promise((resolve, reject) => {
      db.run(`
        INSERT INTO subscription_plans
        (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        testPlan.id,
        testPlan.name,
        testPlan.price,
        testPlan.currency,
        testPlan.billing_period,
        testPlan.max_streaming_slots,
        testPlan.max_storage_gb,
        testPlan.features
      ], function(err) {
        if (err) reject(err);
        else {
          console.log('✓ Test plan created successfully');
          resolve();
        }
      });
    });

    // Verify the plan was created
    const createdPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE id = ?', [testPlan.id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    console.log('✓ Plan verification:');
    console.log(`  - Name: ${createdPlan.name}`);
    console.log(`  - Price: ${createdPlan.price} ${createdPlan.currency}`);
    console.log(`  - Streaming Slots: ${createdPlan.max_streaming_slots}`);
    console.log(`  - Storage: ${createdPlan.max_storage_gb}GB`);

    // Test another plan with price 0 but streaming slots 1
    const testPlan2 = {
      id: uuidv4(),
      name: 'Free Basic Test',
      price: 0,
      currency: 'USD',
      billing_period: 'monthly',
      max_streaming_slots: 1,
      max_storage_gb: 0,
      features: JSON.stringify(['1 Streaming Slot', 'No Storage', 'Basic Support'])
    };

    console.log('\nCreating second test plan:', testPlan2);

    await new Promise((resolve, reject) => {
      db.run(`
        INSERT INTO subscription_plans
        (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        testPlan2.id,
        testPlan2.name,
        testPlan2.price,
        testPlan2.currency,
        testPlan2.billing_period,
        testPlan2.max_streaming_slots,
        testPlan2.max_storage_gb,
        testPlan2.features
      ], function(err) {
        if (err) reject(err);
        else {
          console.log('✓ Second test plan created successfully');
          resolve();
        }
      });
    });

    // Show all plans
    const allPlans = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans ORDER BY price ASC', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n✓ All subscription plans:');
    allPlans.forEach(plan => {
      let currencySymbol = '$';
      if (plan.currency === 'IDR') currencySymbol = 'Rp';
      else if (plan.currency === 'EUR') currencySymbol = '€';
      else if (plan.currency === 'GBP') currencySymbol = '£';
      
      console.log(`  - ${plan.name}: ${currencySymbol}${plan.price} (${plan.max_streaming_slots} slots, ${plan.max_storage_gb}GB)`);
    });

    // Clean up test plans
    console.log('\nCleaning up test plans...');
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM subscription_plans WHERE name LIKE "%Test%"', [], function(err) {
        if (err) reject(err);
        else {
          console.log(`✓ Removed ${this.changes} test plans`);
          resolve();
        }
      });
    });

    console.log('✓ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testPlanCreation().then(() => {
    console.log('Test script finished');
    process.exit(0);
  }).catch(error => {
    console.error('Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testPlanCreation };
