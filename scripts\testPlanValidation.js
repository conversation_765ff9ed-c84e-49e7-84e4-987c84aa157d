const { db } = require('../db/database');
const Subscription = require('../models/Subscription');

async function testPlanValidation() {
  console.log('Testing plan name validation...');
  
  try {
    // Test 1: Check existing plans
    console.log('\n1. Checking existing plans:');
    const allPlans = await Subscription.getAllPlansAdmin();
    console.log(`Found ${allPlans.length} total plans:`);
    allPlans.forEach(plan => {
      console.log(`  - "${plan.name}" (ID: ${plan.id}, Active: ${plan.is_active})`);
    });

    // Test 2: Test case-insensitive search
    console.log('\n2. Testing case-insensitive search:');
    
    if (allPlans.length > 0) {
      const testPlan = allPlans[0];
      const originalName = testPlan.name;
      
      // Test exact match
      const exactMatch = await Subscription.getPlanByName(originalName);
      console.log(`  - Exact match "${originalName}":`, exactMatch ? 'FOUND' : 'NOT FOUND');
      
      // Test uppercase
      const upperMatch = await Subscription.getPlanByName(originalName.toUpperCase());
      console.log(`  - Uppercase "${originalName.toUpperCase()}":`, upperMatch ? 'FOUND' : 'NOT FOUND');
      
      // Test lowercase
      const lowerMatch = await Subscription.getPlanByName(originalName.toLowerCase());
      console.log(`  - Lowercase "${originalName.toLowerCase()}":`, lowerMatch ? 'FOUND' : 'NOT FOUND');
      
      // Test with spaces
      const spaceMatch = await Subscription.getPlanByName(`  ${originalName}  `);
      console.log(`  - With spaces "  ${originalName}  ":`, spaceMatch ? 'FOUND' : 'NOT FOUND');
    }

    // Test 3: Test creating plan with existing name
    console.log('\n3. Testing duplicate name validation:');
    
    if (allPlans.length > 0) {
      const existingPlan = allPlans[0];
      console.log(`  - Trying to create plan with existing name "${existingPlan.name}"`);
      
      try {
        const duplicatePlan = await Subscription.createPlan({
          name: existingPlan.name,
          price: 99.99,
          currency: 'USD',
          billing_period: 'monthly',
          max_streaming_slots: 5,
          max_storage_gb: 100,
          features: ['Test Feature']
        });
        console.log('  ❌ ERROR: Duplicate plan was created!', duplicatePlan.id);
      } catch (error) {
        console.log('  ✓ GOOD: Duplicate creation failed as expected');
        console.log(`    Error: ${error.message}`);
      }
    }

    // Test 4: Test creating plan with case-different name
    console.log('\n4. Testing case-different name validation:');
    
    if (allPlans.length > 0) {
      const existingPlan = allPlans[0];
      const caseDifferentName = existingPlan.name.toUpperCase();
      console.log(`  - Trying to create plan with case-different name "${caseDifferentName}"`);
      
      try {
        const caseDifferentPlan = await Subscription.createPlan({
          name: caseDifferentName,
          price: 99.99,
          currency: 'USD',
          billing_period: 'monthly',
          max_streaming_slots: 5,
          max_storage_gb: 100,
          features: ['Test Feature']
        });
        console.log('  ❌ ERROR: Case-different plan was created!', caseDifferentPlan.id);
        
        // Clean up
        await new Promise((resolve, reject) => {
          db.run('DELETE FROM subscription_plans WHERE id = ?', [caseDifferentPlan.id], (err) => {
            if (err) reject(err);
            else {
              console.log('  🧹 Cleaned up test plan');
              resolve();
            }
          });
        });
      } catch (error) {
        console.log('  ✓ GOOD: Case-different creation failed as expected');
        console.log(`    Error: ${error.message}`);
      }
    }

    // Test 5: Test creating unique plan
    console.log('\n5. Testing unique plan creation:');
    const uniqueName = `Test Plan ${Date.now()}`;
    console.log(`  - Creating plan with unique name "${uniqueName}"`);
    
    try {
      const uniquePlan = await Subscription.createPlan({
        name: uniqueName,
        price: 0,
        currency: 'USD',
        billing_period: 'monthly',
        max_streaming_slots: 1,
        max_storage_gb: 5,
        features: ['Test Feature']
      });
      console.log('  ✓ GOOD: Unique plan created successfully', uniquePlan.id);
      
      // Clean up
      await new Promise((resolve, reject) => {
        db.run('DELETE FROM subscription_plans WHERE id = ?', [uniquePlan.id], (err) => {
          if (err) reject(err);
          else {
            console.log('  🧹 Cleaned up test plan');
            resolve();
          }
        });
      });
    } catch (error) {
      console.log('  ❌ ERROR: Unique plan creation failed');
      console.log(`    Error: ${error.message}`);
    }

    console.log('\n✅ Plan validation test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testPlanValidation().then(() => {
    console.log('Test script finished');
    process.exit(0);
  }).catch(error => {
    console.error('Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testPlanValidation };
