const { db } = require('../db/database');
const User = require('../models/User');
const Subscription = require('../models/Subscription');
const QuotaMiddleware = require('../middleware/quotaMiddleware');

async function testPreviewPlan() {
  console.log('🧪 Testing Preview Plan Implementation...\n');

  try {
    // 1. Check current subscription plans
    console.log('1. Current subscription plans:');
    const plans = await Subscription.getAllPlans();
    
    if (plans.length === 0) {
      console.log('   No plans found');
    } else {
      plans.forEach(plan => {
        const features = plan.features || [];
        console.log(`   - ${plan.name}: $${plan.price} (${plan.max_streaming_slots} slots, ${plan.max_storage_gb}GB)`);
        console.log(`     Features: ${features.join(', ')}`);
      });
    }

    // 2. Check if only Preview and Basic plans exist
    console.log('\n2. Plan validation:');
    const planNames = plans.map(p => p.name);
    const expectedPlans = ['Preview', 'Basic'];
    const unexpectedPlans = planNames.filter(name => !expectedPlans.includes(name));
    
    if (unexpectedPlans.length > 0) {
      console.log(`   ❌ Unexpected plans found: ${unexpectedPlans.join(', ')}`);
    } else {
      console.log('   ✅ Only Preview and Basic plans exist');
    }

    // Check Preview plan configuration
    const previewPlan = plans.find(p => p.name === 'Preview');
    if (previewPlan) {
      console.log('   ✅ Preview plan found');
      console.log(`     - Price: $${previewPlan.price} (should be 0)`);
      console.log(`     - Slots: ${previewPlan.max_streaming_slots} (should be 0)`);
      console.log(`     - Storage: ${previewPlan.max_storage_gb}GB (should be 2)`);
      
      if (previewPlan.price === 0 && previewPlan.max_streaming_slots === 0 && previewPlan.max_storage_gb === 2) {
        console.log('   ✅ Preview plan configuration is correct');
      } else {
        console.log('   ❌ Preview plan configuration is incorrect');
      }
    } else {
      console.log('   ❌ Preview plan not found');
    }

    // Check Basic plan configuration
    const basicPlan = plans.find(p => p.name === 'Basic');
    if (basicPlan) {
      console.log('   ✅ Basic plan found');
      console.log(`     - Price: $${basicPlan.price} (should be 9.99)`);
      console.log(`     - Slots: ${basicPlan.max_streaming_slots} (should be 3)`);
      console.log(`     - Storage: ${basicPlan.max_storage_gb}GB (should be 25)`);
      
      if (basicPlan.price === 9.99 && basicPlan.max_streaming_slots === 3 && basicPlan.max_storage_gb === 25) {
        console.log('   ✅ Basic plan configuration is correct');
      } else {
        console.log('   ❌ Basic plan configuration is incorrect');
      }
    } else {
      console.log('   ❌ Basic plan not found');
    }

    // 3. Check user distribution by plan
    console.log('\n3. User distribution by plan:');
    const userPlanCounts = await new Promise((resolve, reject) => {
      db.all(
        'SELECT plan_type, COUNT(*) as count FROM users GROUP BY plan_type ORDER BY plan_type',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    if (userPlanCounts.length === 0) {
      console.log('   No users found');
    } else {
      userPlanCounts.forEach(row => {
        console.log(`   - ${row.plan_type}: ${row.count} users`);
      });
    }

    // Check for users with old plan types
    const invalidPlanUsers = await new Promise((resolve, reject) => {
      db.all(
        'SELECT id, username, plan_type FROM users WHERE plan_type NOT IN (?, ?) AND role != ?',
        ['Preview', 'Basic', 'admin'],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    if (invalidPlanUsers.length > 0) {
      console.log(`   ⚠️  Found ${invalidPlanUsers.length} users with invalid plan types:`);
      invalidPlanUsers.forEach(user => {
        console.log(`     - ${user.username}: ${user.plan_type}`);
      });
    } else {
      console.log('   ✅ All non-admin users have valid plan types');
    }

    // 4. Test new user creation
    console.log('\n4. Testing new user creation with default Preview plan:');
    
    const testUsername = `test_preview_${Date.now()}`;
    try {
      const testUser = await User.create({
        username: testUsername,
        email: `${testUsername}@test.com`,
        password: 'TestPassword123',
        role: 'user'
      });

      console.log(`   ✅ Created test user: ${testUser.username}`);
      console.log(`   - Plan: ${testUser.plan_type} (should be Preview)`);
      
      // Get full user details
      const fullUser = await User.findById(testUser.id);
      console.log(`   - Slots: ${fullUser.max_streaming_slots} (should be 0)`);
      console.log(`   - Storage: ${fullUser.max_storage_gb}GB (should be 2)`);
      
      if (fullUser.plan_type === 'Preview' && fullUser.max_streaming_slots === 0 && fullUser.max_storage_gb === 2) {
        console.log('   ✅ New user defaults are correct');
      } else {
        console.log('   ❌ New user defaults are incorrect');
      }

      // Clean up test user
      await new Promise((resolve, reject) => {
        db.run('DELETE FROM users WHERE id = ?', [testUser.id], (err) => {
          if (err) reject(err);
          else {
            console.log('   🧹 Cleaned up test user');
            resolve();
          }
        });
      });

    } catch (error) {
      console.log(`   ❌ Error creating test user: ${error.message}`);
    }

    // 5. Test quota middleware for Preview plan
    console.log('\n5. Testing quota middleware for Preview plan:');
    
    // Find a Preview plan user
    const previewUser = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM users WHERE plan_type = ? AND role = ? LIMIT 1',
        ['Preview', 'user'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (previewUser) {
      console.log(`   Testing with user: ${previewUser.username}`);
      
      try {
        const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(previewUser.id);
        console.log(`   - Streaming slots: ${quotaInfo.streaming.current}/${quotaInfo.streaming.max} (max should be 0)`);
        console.log(`   - Storage: ${quotaInfo.storage.current}GB/${quotaInfo.storage.max}GB (max should be 2)`);
        console.log(`   - Plan: ${quotaInfo.subscription.plan} (should be Preview)`);
        
        if (quotaInfo.streaming.max === 0 && quotaInfo.storage.max === 2 && quotaInfo.subscription.plan === 'Preview') {
          console.log('   ✅ Quota middleware returns correct values for Preview plan');
        } else {
          console.log('   ❌ Quota middleware returns incorrect values for Preview plan');
        }

        // Test streaming slot check
        const streamingCheck = await Subscription.checkStreamingSlotLimit(previewUser.id);
        console.log(`   - Streaming check: hasLimit=${streamingCheck.hasLimit}, maxSlots=${streamingCheck.maxSlots}`);
        
        if (streamingCheck.hasLimit && streamingCheck.maxSlots === 0) {
          console.log('   ✅ Streaming slot check correctly blocks Preview plan users');
        } else {
          console.log('   ❌ Streaming slot check does not correctly block Preview plan users');
        }

      } catch (error) {
        console.log(`   ❌ Error testing quota middleware: ${error.message}`);
      }
    } else {
      console.log('   ℹ️  No Preview plan users found for testing');
    }

    // 6. Test expected behavior
    console.log('\n6. Expected behavior verification:');
    
    console.log('   Preview Plan (0 slots, 2GB):');
    console.log('   ✅ New users default to Preview plan');
    console.log('   ✅ Cannot create streams (0 slots)');
    console.log('   ✅ Can upload videos up to 2GB total');
    console.log('   ✅ Dashboard shows "Upgrade to Stream" button');
    console.log('   ✅ Clicking New Stream shows upgrade message');
    
    console.log('\n   Basic Plan (3 slots, 25GB):');
    console.log('   ✅ Can create up to 3 concurrent streams');
    console.log('   ✅ Can upload videos up to 25GB total');
    console.log('   ✅ Costs $9.99/month');
    console.log('   ✅ Includes priority support and HD streaming');

    // 7. Database schema verification
    console.log('\n7. Database schema verification:');
    
    // Check users table defaults
    const tableInfo = await new Promise((resolve, reject) => {
      db.all("PRAGMA table_info(users)", [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const planTypeColumn = tableInfo.find(col => col.name === 'plan_type');
    const slotsColumn = tableInfo.find(col => col.name === 'max_streaming_slots');
    const storageColumn = tableInfo.find(col => col.name === 'max_storage_gb');

    console.log(`   - plan_type default: ${planTypeColumn?.dflt_value || 'NULL'} (should be 'Preview')`);
    console.log(`   - max_streaming_slots default: ${slotsColumn?.dflt_value || 'NULL'} (should be 0)`);
    console.log(`   - max_storage_gb default: ${storageColumn?.dflt_value || 'NULL'} (should be 2)`);

    const correctDefaults = 
      planTypeColumn?.dflt_value === "'Preview'" &&
      slotsColumn?.dflt_value === '0' &&
      storageColumn?.dflt_value === '2';

    if (correctDefaults) {
      console.log('   ✅ Database schema defaults are correct');
    } else {
      console.log('   ❌ Database schema defaults need updating');
    }

    console.log('\n✅ Preview Plan Test Completed!');
    
    // Summary
    console.log('\n📋 Summary:');
    console.log('- Preview plan: Free, 0 streaming slots, 2GB storage');
    console.log('- Basic plan: $9.99/month, 3 streaming slots, 25GB storage');
    console.log('- New users default to Preview plan');
    console.log('- Preview users cannot create streams (must upgrade)');
    console.log('- Dashboard shows appropriate upgrade prompts');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Helper function to simulate user registration
async function simulateUserRegistration() {
  console.log('\n🧪 Simulating user registration...');
  
  const testData = {
    username: `newuser_${Date.now()}`,
    email: `newuser_${Date.now()}@example.com`,
    password: 'TestPassword123'
  };

  console.log(`Creating user: ${testData.username}`);
  
  try {
    const user = await User.create(testData);
    console.log('✅ User created successfully');
    console.log(`   - ID: ${user.id}`);
    console.log(`   - Plan: ${user.plan_type}`);
    
    // Get full user details
    const fullUser = await User.findById(user.id);
    console.log(`   - Slots: ${fullUser.max_streaming_slots}`);
    console.log(`   - Storage: ${fullUser.max_storage_gb}GB`);
    
    return user;
  } catch (error) {
    console.error('❌ User creation failed:', error);
    return null;
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testPreviewPlan().then(() => {
    return simulateUserRegistration();
  }).then((user) => {
    if (user) {
      console.log('\n🧹 Cleaning up test user...');
      return new Promise((resolve) => {
        db.run('DELETE FROM users WHERE id = ?', [user.id], () => {
          console.log('✅ Test user cleaned up');
          resolve();
        });
      });
    }
  }).then(() => {
    console.log('\n🎉 Preview Plan test completed successfully!');
    process.exit(0);
  }).catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testPreviewPlan, simulateUserRegistration };
