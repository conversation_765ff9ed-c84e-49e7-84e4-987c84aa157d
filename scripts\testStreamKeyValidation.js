const StreamKeyValidator = require('../utils/streamKeyValidator');

async function testStreamKeyValidation() {
  console.log('Testing Stream Key Validation...\n');

  // Test cases
  const testCases = [
    {
      name: 'Valid YouTube Configuration',
      rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
      streamKey: 'abcd-1234-efgh-5678-ijkl-9012',
      expectedValid: true
    },
    {
      name: 'Invalid YouTube Stream Key (too short)',
      rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
      streamKey: '123',
      expectedValid: false
    },
    {
      name: 'Placeholder Stream Key',
      rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
      streamKey: 'your_stream_key',
      expectedValid: false
    },
    {
      name: 'Valid Twitch Configuration',
      rtmpUrl: 'rtmp://live.twitch.tv/live',
      streamKey: 'live_123456789_abcdefghijklmnopqrstuvwxyz',
      expectedValid: true
    },
    {
      name: 'Invalid Twitch Stream Key Format',
      rtmpUrl: 'rtmp://live.twitch.tv/live',
      streamKey: 'invalid_twitch_key',
      expectedValid: false
    },
    {
      name: 'Valid Facebook Configuration',
      rtmpUrl: 'rtmps://live-api-s.facebook.com:443/rtmp',
      streamKey: 'FB-123456789-ABCDEFGHIJKLMNOP',
      expectedValid: true
    },
    {
      name: 'Invalid RTMP URL',
      rtmpUrl: 'http://invalid-url.com',
      streamKey: 'valid_stream_key_123',
      expectedValid: false
    },
    {
      name: 'Empty Stream Key',
      rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
      streamKey: '',
      expectedValid: false
    },
    {
      name: 'Custom RTMP Server',
      rtmpUrl: 'rtmp://custom-server.example.com/live',
      streamKey: 'custom_key_12345',
      expectedValid: true
    },
    {
      name: 'Local RTMP URL (Warning)',
      rtmpUrl: 'rtmp://localhost:1935/live',
      streamKey: 'local_test_key',
      expectedValid: false
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    console.log(`🧪 Testing: ${testCase.name}`);
    console.log(`   RTMP URL: ${testCase.rtmpUrl}`);
    console.log(`   Stream Key: ${testCase.streamKey}`);

    try {
      const validation = StreamKeyValidator.validateRtmpConfig(testCase.rtmpUrl, testCase.streamKey);
      
      console.log(`   Platform: ${validation.platform || 'Unknown'}`);
      console.log(`   Valid: ${validation.isValid}`);
      
      if (validation.errors && validation.errors.length > 0) {
        console.log(`   Errors:`);
        validation.errors.forEach(error => {
          console.log(`     - ${error}`);
        });
      }
      
      if (validation.warnings && validation.warnings.length > 0) {
        console.log(`   Warnings:`);
        validation.warnings.forEach(warning => {
          console.log(`     - ${warning}`);
        });
      }
      
      if (validation.suggestions && validation.suggestions.length > 0) {
        console.log(`   Suggestions:`);
        validation.suggestions.forEach(suggestion => {
          console.log(`     - ${suggestion}`);
        });
      }

      // Check if result matches expectation
      const testPassed = validation.isValid === testCase.expectedValid;
      if (testPassed) {
        console.log(`   ✅ Test PASSED`);
        passedTests++;
      } else {
        console.log(`   ❌ Test FAILED (Expected: ${testCase.expectedValid}, Got: ${validation.isValid})`);
      }

    } catch (error) {
      console.log(`   ❌ Test ERROR: ${error.message}`);
    }

    console.log(''); // Empty line for readability
  }

  // Test individual validation functions
  console.log('🔍 Testing Individual Validation Functions:\n');

  // Test platform detection
  console.log('Platform Detection:');
  const platformTests = [
    { url: 'rtmp://a.rtmp.youtube.com/live2', expected: 'youtube' },
    { url: 'rtmp://live.twitch.tv/live', expected: 'twitch' },
    { url: 'rtmps://live-api-s.facebook.com:443/rtmp', expected: 'facebook' },
    { url: 'rtmp://custom-server.com/live', expected: 'custom' }
  ];

  platformTests.forEach(test => {
    const detected = StreamKeyValidator.detectPlatform(test.url);
    const passed = detected === test.expected;
    console.log(`  ${test.url} → ${detected} ${passed ? '✅' : '❌'}`);
  });

  // Test RTMP URL validation
  console.log('\nRTMP URL Validation:');
  const rtmpTests = [
    { url: 'rtmp://valid-server.com/live', shouldPass: true },
    { url: 'rtmps://secure-server.com/live', shouldPass: true },
    { url: 'http://invalid-protocol.com', shouldPass: false },
    { url: '', shouldPass: false },
    { url: 'rtmp://localhost/live', shouldPass: false } // Should have warning
  ];

  rtmpTests.forEach(test => {
    const validation = StreamKeyValidator.validateRtmpUrl(test.url);
    const passed = validation.isValid === test.shouldPass;
    console.log(`  ${test.url || '(empty)'} → ${validation.isValid ? 'Valid' : 'Invalid'} ${passed ? '✅' : '❌'}`);
    if (validation.errors.length > 0) {
      console.log(`    Errors: ${validation.errors.join(', ')}`);
    }
  });

  // Test stream key validation
  console.log('\nStream Key Validation:');
  const keyTests = [
    { key: 'valid_key_123', shouldPass: true },
    { key: '123', shouldPass: false }, // Too short
    { key: 'test', shouldPass: false }, // Placeholder
    { key: '', shouldPass: false }, // Empty
    { key: 'key with spaces', shouldPass: false }, // Contains spaces
    { key: 'a'.repeat(101), shouldPass: false } // Too long
  ];

  keyTests.forEach(test => {
    const validation = StreamKeyValidator.validateStreamKey(test.key);
    const passed = validation.isValid === test.shouldPass;
    console.log(`  "${test.key}" → ${validation.isValid ? 'Valid' : 'Invalid'} ${passed ? '✅' : '❌'}`);
    if (validation.errors.length > 0) {
      console.log(`    Errors: ${validation.errors.join(', ')}`);
    }
  });

  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Stream key validation is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the validation logic.');
  }

  // Test platform help
  console.log('\n📚 Platform Help Examples:');
  const platforms = ['youtube', 'twitch', 'facebook', 'custom'];
  platforms.forEach(platform => {
    const help = StreamKeyValidator.getPlatformHelp(platform);
    console.log(`\n${platform.toUpperCase()}:`);
    console.log(`  RTMP URL: ${help.rtmpUrl}`);
    console.log(`  Instructions: ${help.instructions}`);
    console.log(`  Key Format: ${help.keyFormat}`);
  });

  console.log('\n✅ Stream Key Validation Test Completed!');
}

// Run test if this script is executed directly
if (require.main === module) {
  testStreamKeyValidation().then(() => {
    console.log('Test script finished');
    process.exit(0);
  }).catch(error => {
    console.error('Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testStreamKeyValidation };
