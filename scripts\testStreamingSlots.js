const { db } = require('../db/database');
const Subscription = require('../models/Subscription');
const Stream = require('../models/Stream');
const { v4: uuidv4 } = require('uuid');

async function testStreamingSlots() {
  console.log('Testing streaming slots calculation...');
  
  try {
    // Get a test user (or create one)
    let testUser = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM users LIMIT 1', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!testUser) {
      console.log('No users found, creating test user...');
      const testUserId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(`
          INSERT INTO users (id, username, email, password, max_streaming_slots, max_storage_gb)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [testUserId, 'testuser', '<EMAIL>', 'password', 3, 10], function(err) {
          if (err) reject(err);
          else {
            testUser = { id: testUserId, username: 'testuser', max_streaming_slots: 3 };
            resolve();
          }
        });
      });
    }

    console.log(`Using test user: ${testUser.username} (ID: ${testUser.id})`);

    // Check initial quota
    console.log('\n1. Initial quota check:');
    let quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`  - Current slots: ${quotaCheck.currentSlots}`);
    console.log(`  - Max slots: ${quotaCheck.maxSlots}`);
    console.log(`  - Has limit: ${quotaCheck.hasLimit}`);

    // Get current streams
    const currentStreams = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`\n2. Current streams for user (${currentStreams.length} total):`);
    currentStreams.forEach(stream => {
      console.log(`  - ${stream.title}: ${stream.status} (ID: ${stream.id})`);
    });

    // Create a test stream
    console.log('\n3. Creating test stream...');
    const testStreamData = {
      title: `Test Stream ${Date.now()}`,
      rtmp_url: 'rtmp://test.example.com/live',
      stream_key: 'test_key_' + Date.now(),
      platform: 'Test Platform',
      user_id: testUser.id
    };

    const newStream = await Stream.create(testStreamData);
    console.log(`  ✓ Created stream: ${newStream.title} (Status: ${newStream.status})`);

    // Check quota after creating stream
    console.log('\n4. Quota check after creating stream:');
    quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`  - Current slots: ${quotaCheck.currentSlots}`);
    console.log(`  - Max slots: ${quotaCheck.maxSlots}`);
    console.log(`  - Has limit: ${quotaCheck.hasLimit}`);

    // Test different stream statuses
    console.log('\n5. Testing stream status changes...');
    
    // Change to live
    await Stream.updateStatus(newStream.id, 'live', testUser.id);
    console.log('  - Changed stream to LIVE');
    quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`    Current slots: ${quotaCheck.currentSlots}`);

    // Change to scheduled
    await Stream.updateStatus(newStream.id, 'scheduled', testUser.id);
    console.log('  - Changed stream to SCHEDULED');
    quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`    Current slots: ${quotaCheck.currentSlots}`);

    // Change back to offline
    await Stream.updateStatus(newStream.id, 'offline', testUser.id);
    console.log('  - Changed stream to OFFLINE');
    quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`    Current slots: ${quotaCheck.currentSlots}`);

    // Test live streams count
    console.log('\n6. Testing live streams count...');
    const liveCount = await Subscription.getLiveStreamsCount(testUser.id);
    console.log(`  - Live streams count: ${liveCount}`);

    // Create multiple streams to test limit
    console.log('\n7. Testing slot limits...');
    const maxSlots = quotaCheck.maxSlots === 'Unlimited' ? 5 : quotaCheck.maxSlots;
    const streamsToCreate = Math.max(0, maxSlots - quotaCheck.currentSlots + 1); // Try to exceed limit

    console.log(`  - Max slots: ${maxSlots}`);
    console.log(`  - Current slots: ${quotaCheck.currentSlots}`);
    console.log(`  - Will try to create ${streamsToCreate} more streams`);

    const createdStreams = [];
    for (let i = 0; i < streamsToCreate; i++) {
      try {
        const streamData = {
          title: `Test Stream ${Date.now()}_${i}`,
          rtmp_url: 'rtmp://test.example.com/live',
          stream_key: `test_key_${Date.now()}_${i}`,
          platform: 'Test Platform',
          user_id: testUser.id
        };

        const stream = await Stream.create(streamData);
        createdStreams.push(stream);
        console.log(`    ✓ Created stream ${i + 1}: ${stream.title}`);

        // Check quota after each creation
        const currentQuota = await Subscription.checkStreamingSlotLimit(testUser.id);
        console.log(`      Slots: ${currentQuota.currentSlots}/${currentQuota.maxSlots}, Has limit: ${currentQuota.hasLimit}`);

      } catch (error) {
        console.log(`    ❌ Failed to create stream ${i + 1}: ${error.message}`);
        break;
      }
    }

    // Final quota check
    console.log('\n8. Final quota check:');
    const finalQuota = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`  - Current slots: ${finalQuota.currentSlots}`);
    console.log(`  - Max slots: ${finalQuota.maxSlots}`);
    console.log(`  - Has limit: ${finalQuota.hasLimit}`);

    // Clean up test streams
    console.log('\n9. Cleaning up test streams...');
    const allTestStreams = [newStream, ...createdStreams];
    for (const stream of allTestStreams) {
      try {
        await Stream.delete(stream.id, testUser.id);
        console.log(`  ✓ Deleted stream: ${stream.title}`);
      } catch (error) {
        console.log(`  ❌ Failed to delete stream ${stream.title}: ${error.message}`);
      }
    }

    // Final verification
    console.log('\n10. Final verification:');
    const finalCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`  - Current slots after cleanup: ${finalCheck.currentSlots}`);

    console.log('\n✅ Streaming slots test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testStreamingSlots().then(() => {
    console.log('Test script finished');
    process.exit(0);
  }).catch(error => {
    console.error('Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testStreamingSlots };
