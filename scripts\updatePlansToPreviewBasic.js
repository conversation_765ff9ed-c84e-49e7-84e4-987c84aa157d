const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

async function updatePlansToPreviewBasic() {
  console.log('🔄 Updating subscription plans to Preview and Basic only...\n');

  try {
    // 1. Check current plans
    console.log('1. Current subscription plans:');
    const currentPlans = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans ORDER BY price ASC', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (currentPlans.length === 0) {
      console.log('   No plans found in database');
    } else {
      currentPlans.forEach(plan => {
        console.log(`   - ${plan.name}: $${plan.price} (${plan.max_streaming_slots} slots, ${plan.max_storage_gb}GB)`);
      });
    }

    // 2. Delete all existing plans except Preview and Basic
    console.log('\n2. Removing old plans (Free, Pro, Enterprise)...');
    
    const plansToDelete = ['Free', 'Pro', 'Enterprise'];
    for (const planName of plansToDelete) {
      try {
        const result = await new Promise((resolve, reject) => {
          db.run('DELETE FROM subscription_plans WHERE name = ?', [planName], function(err) {
            if (err) reject(err);
            else resolve(this.changes);
          });
        });
        
        if (result > 0) {
          console.log(`   ✅ Deleted ${planName} plan`);
        } else {
          console.log(`   ℹ️  ${planName} plan not found (already deleted)`);
        }
      } catch (error) {
        console.log(`   ❌ Error deleting ${planName} plan: ${error.message}`);
      }
    }

    // 3. Check if Preview plan exists, if not create it
    console.log('\n3. Ensuring Preview plan exists...');
    
    const previewPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = ?', ['Preview'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!previewPlan) {
      console.log('   Creating Preview plan...');
      const previewPlanData = {
        id: uuidv4(),
        name: 'Preview',
        price: 0,
        currency: 'USD',
        billing_period: 'monthly',
        max_streaming_slots: 0,
        max_storage_gb: 2,
        features: JSON.stringify(['0 Streaming Slots', '2GB Storage', 'Preview Only', 'Basic Support'])
      };

      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO subscription_plans
           (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [previewPlanData.id, previewPlanData.name, previewPlanData.price, previewPlanData.currency,
           previewPlanData.billing_period, previewPlanData.max_streaming_slots, previewPlanData.max_storage_gb, previewPlanData.features],
          function(err) {
            if (err) reject(err);
            else {
              console.log('   ✅ Preview plan created successfully');
              resolve();
            }
          }
        );
      });
    } else {
      console.log('   ✅ Preview plan already exists');
      
      // Update Preview plan to ensure correct values
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE subscription_plans 
           SET price = 0, max_streaming_slots = 0, max_storage_gb = 2,
               features = ?, updated_at = CURRENT_TIMESTAMP
           WHERE name = 'Preview'`,
          [JSON.stringify(['0 Streaming Slots', '2GB Storage', 'Preview Only', 'Basic Support'])],
          function(err) {
            if (err) reject(err);
            else {
              console.log('   ✅ Preview plan updated to correct values');
              resolve();
            }
          }
        );
      });
    }

    // 4. Check if Basic plan exists, if not create it
    console.log('\n4. Ensuring Basic plan exists...');
    
    const basicPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = ?', ['Basic'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!basicPlan) {
      console.log('   Creating Basic plan...');
      const basicPlanData = {
        id: uuidv4(),
        name: 'Basic',
        price: 9.99,
        currency: 'USD',
        billing_period: 'monthly',
        max_streaming_slots: 3,
        max_storage_gb: 25,
        features: JSON.stringify(['3 Streaming Slots', '25GB Storage', 'Priority Support', 'HD Streaming'])
      };

      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO subscription_plans
           (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [basicPlanData.id, basicPlanData.name, basicPlanData.price, basicPlanData.currency,
           basicPlanData.billing_period, basicPlanData.max_streaming_slots, basicPlanData.max_storage_gb, basicPlanData.features],
          function(err) {
            if (err) reject(err);
            else {
              console.log('   ✅ Basic plan created successfully');
              resolve();
            }
          }
        );
      });
    } else {
      console.log('   ✅ Basic plan already exists');
      
      // Update Basic plan to ensure correct values
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE subscription_plans 
           SET price = 9.99, max_streaming_slots = 3, max_storage_gb = 25,
               features = ?, updated_at = CURRENT_TIMESTAMP
           WHERE name = 'Basic'`,
          [JSON.stringify(['3 Streaming Slots', '25GB Storage', 'Priority Support', 'HD Streaming'])],
          function(err) {
            if (err) reject(err);
            else {
              console.log('   ✅ Basic plan updated to correct values');
              resolve();
            }
          }
        );
      });
    }

    // 5. Update users with 'Free' plan to 'Preview' plan
    console.log('\n5. Updating users from Free plan to Preview plan...');
    
    const usersToUpdate = await new Promise((resolve, reject) => {
      db.all('SELECT id, username, plan_type FROM users WHERE plan_type = ?', ['Free'], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (usersToUpdate.length === 0) {
      console.log('   No users with Free plan found');
    } else {
      console.log(`   Found ${usersToUpdate.length} users with Free plan`);
      
      for (const user of usersToUpdate) {
        try {
          await new Promise((resolve, reject) => {
            db.run(
              `UPDATE users 
               SET plan_type = 'Preview', max_streaming_slots = 0, max_storage_gb = 2,
                   updated_at = CURRENT_TIMESTAMP
               WHERE id = ?`,
              [user.id],
              function(err) {
                if (err) reject(err);
                else {
                  console.log(`   ✅ Updated user ${user.username} from Free to Preview plan`);
                  resolve();
                }
              }
            );
          });
        } catch (error) {
          console.log(`   ❌ Error updating user ${user.username}: ${error.message}`);
        }
      }
    }

    // 6. Verify final state
    console.log('\n6. Final verification:');
    
    const finalPlans = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans ORDER BY price ASC', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('   Current subscription plans:');
    finalPlans.forEach(plan => {
      const features = JSON.parse(plan.features || '[]');
      console.log(`   - ${plan.name}: $${plan.price} (${plan.max_streaming_slots} slots, ${plan.max_storage_gb}GB)`);
      console.log(`     Features: ${features.join(', ')}`);
    });

    const userPlanCounts = await new Promise((resolve, reject) => {
      db.all(
        'SELECT plan_type, COUNT(*) as count FROM users GROUP BY plan_type ORDER BY plan_type',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    console.log('\n   User distribution by plan:');
    userPlanCounts.forEach(row => {
      console.log(`   - ${row.plan_type}: ${row.count} users`);
    });

    console.log('\n✅ Plan update completed successfully!');
    console.log('\nSummary:');
    console.log('- Removed: Free, Pro, Enterprise plans');
    console.log('- Kept/Created: Preview (0 slots, 2GB) and Basic (3 slots, 25GB) plans');
    console.log('- Updated all Free plan users to Preview plan');
    console.log('- New users will default to Preview plan');

  } catch (error) {
    console.error('❌ Plan update failed:', error);
    throw error;
  }
}

// Helper function to check plan consistency
async function checkPlanConsistency() {
  console.log('\n🔍 Checking plan consistency...');
  
  try {
    // Check for orphaned user subscriptions
    const orphanedSubscriptions = await new Promise((resolve, reject) => {
      db.all(`
        SELECT us.*, sp.name as plan_name 
        FROM user_subscriptions us 
        LEFT JOIN subscription_plans sp ON us.plan_id = sp.id 
        WHERE sp.id IS NULL
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (orphanedSubscriptions.length > 0) {
      console.log(`   ⚠️  Found ${orphanedSubscriptions.length} orphaned subscriptions`);
      console.log('   These subscriptions reference deleted plans and should be cleaned up');
    } else {
      console.log('   ✅ No orphaned subscriptions found');
    }

    // Check for users with invalid plan types
    const invalidPlanUsers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT u.id, u.username, u.plan_type 
        FROM users u 
        LEFT JOIN subscription_plans sp ON u.plan_type = sp.name 
        WHERE sp.name IS NULL AND u.plan_type NOT IN ('Preview', 'Basic')
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (invalidPlanUsers.length > 0) {
      console.log(`   ⚠️  Found ${invalidPlanUsers.length} users with invalid plan types`);
      invalidPlanUsers.forEach(user => {
        console.log(`     - ${user.username}: ${user.plan_type}`);
      });
    } else {
      console.log('   ✅ All users have valid plan types');
    }

  } catch (error) {
    console.error('   ❌ Error checking consistency:', error);
  }
}

// Run script if executed directly
if (require.main === module) {
  updatePlansToPreviewBasic().then(() => {
    return checkPlanConsistency();
  }).then(() => {
    console.log('\n🎉 Plan update script completed successfully!');
    process.exit(0);
  }).catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { updatePlansToPreviewBasic, checkPlanConsistency };
