// Test script untuk debugging API endpoints
const http = require('http');

function testAPI(path, callback) {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: path,
    method: 'GET',
    headers: {
      'Cookie': 'connect.sid=your-session-id-here' // Ganti dengan session ID yang valid
    }
  };

  const req = http.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log(`\n=== Testing ${path} ===`);
      console.log(`Status: ${res.statusCode}`);
      console.log(`Headers:`, res.headers);
      
      try {
        const jsonData = JSON.parse(data);
        console.log(`Response:`, JSON.stringify(jsonData, null, 2));
      } catch (e) {
        console.log(`Raw Response:`, data);
      }
      
      if (callback) callback();
    });
  });

  req.on('error', (e) => {
    console.error(`Error testing ${path}:`, e.message);
    if (callback) callback();
  });

  req.end();
}

// Test endpoints
console.log('Testing API endpoints...');

testAPI('/api/streams', () => {
  testAPI('/api/streams/status', () => {
    console.log('\nTesting completed!');
  });
});
