const ffmpeg = require('fluent-ffmpeg');
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const { getVideoDurationInSeconds } = require('get-video-duration');
const fs = require('fs');
const path = require('path');
const { getUniqueFilename, paths } = require('./storage');
ffmpeg.setFfmpegPath(ffmpegPath);
const getVideoInfo = async (filepath) => {
  try {
    const duration = await getVideoDurationInSeconds(filepath);
    const stats = fs.statSync(filepath);
    const fileSizeInBytes = stats.size;

    // Get detailed video information including codec
    const videoDetails = await getDetailedVideoInfo(filepath);

    return {
      duration,
      fileSize: fileSizeInBytes,
      ...videoDetails
    };
  } catch (error) {
    console.error('Error getting video info:', error);
    throw error;
  }
};

const getDetailedVideoInfo = (filepath) => {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filepath, (err, metadata) => {
      if (err) {
        console.error('Error getting video metadata:', err);
        resolve({}); // Return empty object instead of rejecting
        return;
      }

      try {
        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');

        const result = {};

        if (videoStream) {
          result.codec = videoStream.codec_name;
          result.resolution = `${videoStream.width}x${videoStream.height}`;
          result.fps = eval(videoStream.r_frame_rate) || 30; // Convert fraction to number
          result.bitrate = parseInt(videoStream.bit_rate) || null;
        }

        if (audioStream) {
          result.audioCodec = audioStream.codec_name;
          result.audioBitrate = parseInt(audioStream.bit_rate) || null;
        }

        console.log(`[VideoProcessor] Video info for ${path.basename(filepath)}:`, {
          codec: result.codec,
          resolution: result.resolution,
          fps: result.fps,
          bitrate: result.bitrate
        });

        resolve(result);
      } catch (parseError) {
        console.error('Error parsing video metadata:', parseError);
        resolve({});
      }
    });
  });
};
const generateThumbnail = (videoPath, thumbnailName) => {
  return new Promise((resolve, reject) => {
    const thumbnailPath = path.join(paths.thumbnails, thumbnailName);
    ffmpeg(videoPath)
      .inputOptions([
        '-hwaccel', 'none', // Use software decoding for compatibility
        '-ss', '00:00:10'   // Seek to 10 seconds for better thumbnail
      ])
      .outputOptions([
        '-vframes', '1',    // Extract only 1 frame
        '-q:v', '2',        // High quality JPEG
        '-vf', 'scale=854:480:force_original_aspect_ratio=decrease,pad=854:480:(ow-iw)/2:(oh-ih)/2' // Maintain aspect ratio with padding
      ])
      .output(thumbnailPath)
      .on('end', () => {
        resolve(thumbnailPath);
      })
      .on('error', (err) => {
        console.error('Error generating thumbnail:', err);
        reject(err);
      })
      .run();
  });
};
module.exports = {
  getVideoInfo,
  getDetailedVideoInfo,
  generateThumbnail
};