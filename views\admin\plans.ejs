<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">Subscription Plans</h1>
      <p class="text-gray-400 mt-1">Manage subscription plans and pricing</p>
    </div>
    <div class="flex items-center space-x-4">
      <div id="planBulkActions" class="hidden flex items-center space-x-2">
        <select id="planBulkActionSelect" class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg">
          <option value="">Select Action</option>
          <option value="activate">Activate Plans</option>
          <option value="deactivate">Deactivate Plans</option>
          <option value="delete">Delete Plans</option>
        </select>
        <button onclick="executePlanBulkAction()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
          Apply
        </button>
        <button onclick="clearPlanSelection()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
          Clear
        </button>
      </div>
      <button onclick="addNewPlan()" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors">
        <i class="ti ti-plus mr-2"></i>
        Add Plan
      </button>
    </div>
  </div>
</div>

<!-- Plans Overview -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <% plans.forEach(function(plan) { %>
    <div class="bg-dark-800 rounded-lg border <%= plan.name === 'Pro' ? 'border-primary' : 'border-gray-700' %> relative overflow-hidden">
      <% if (plan.name === 'Pro') { %>
        <div class="absolute top-0 left-0 right-0 bg-primary text-white text-center py-2 text-sm font-medium">
          Most Popular
        </div>
      <% } %>

      <div class="p-6 <%= plan.name === 'Pro' ? 'pt-12' : '' %>">
        <!-- Plan Header -->
        <div class="text-center mb-6">
          <h3 class="text-xl font-bold text-white mb-2"><%= plan.name %></h3>
          <div class="mb-4">
            <span class="text-3xl font-bold text-white">$<%= plan.price %></span>
            <span class="text-gray-400">/<%= plan.billing_period %></span>
          </div>
        </div>

        <!-- Features -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-sm">
            <i class="ti ti-check text-green-400 mr-2"></i>
            <span class="text-gray-300">
              <%= plan.max_streaming_slots === -1 ? 'Unlimited' : plan.max_streaming_slots %> Streaming Slot<%= plan.max_streaming_slots !== 1 ? 's' : '' %>
            </span>
          </div>
          <div class="flex items-center text-sm">
            <i class="ti ti-check text-green-400 mr-2"></i>
            <span class="text-gray-300"><%= plan.max_storage_gb %>GB Storage</span>
          </div>
          <div class="flex items-center text-sm">
            <i class="ti ti-check text-green-400 mr-2"></i>
            <span class="text-gray-300">24/7 Support</span>
          </div>
        </div>

        <!-- Stats -->
        <div class="border-t border-gray-700 pt-4">
          <div class="text-center">
            <p class="text-sm text-gray-400">Active Subscribers</p>
            <p class="text-lg font-bold text-white"><%= plan.subscriber_count || 0 %></p>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-4 flex space-x-2">
          <button onclick="editPlan('<%= plan.id %>')" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors text-sm">
            <i class="ti ti-edit mr-1"></i>
            Edit
          </button>
          <% if (plan.name !== 'Free') { %>
            <button onclick="deletePlan('<%= plan.id %>')" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors text-sm">
              <i class="ti ti-trash"></i>
            </button>
          <% } %>
        </div>
      </div>
    </div>
  <% }); %>
</div>

<!-- Plans Table -->
<div class="bg-dark-800 rounded-lg border border-gray-700">
  <div class="p-6 border-b border-gray-700">
    <h3 class="text-lg font-semibold text-white">Plan Details</h3>
  </div>

  <div class="overflow-x-auto">
    <table class="min-w-full">
      <thead class="bg-gray-700">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
            <input type="checkbox" id="selectAllPlans" onchange="toggleSelectAllPlans()"
                   class="rounded border-gray-600 bg-dark-700 text-primary focus:ring-primary">
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Plan Name</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Price</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Streaming Slots</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Storage</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Subscribers</th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-700">
        <% plans.forEach(function(plan) { %>
          <tr class="hover:bg-dark-700/50 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap">
              <input type="checkbox" class="plan-checkbox rounded border-gray-600 bg-dark-700 text-primary focus:ring-primary"
                     value="<%= plan.id %>" onchange="updatePlanBulkActions()" <%= plan.name.toLowerCase() === 'free' || plan.name.toLowerCase().includes('free') ? 'disabled' : '' %>>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-<%= plan.name === 'Free' ? 'gray' : plan.name === 'Basic' ? 'blue' : plan.name === 'Pro' ? 'purple' : 'yellow' %>-500 rounded-lg flex items-center justify-center">
                  <i class="ti ti-<%= plan.name === 'Free' ? 'gift' : plan.name === 'Basic' ? 'star' : plan.name === 'Pro' ? 'crown' : 'diamond' %> text-white"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-white"><%= plan.name %></div>
                  <div class="text-sm text-gray-400"><%= plan.description || 'No description' %></div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white">$<%= plan.price %></div>
              <div class="text-sm text-gray-400">per <%= plan.billing_period %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white">
                <%= plan.max_streaming_slots === -1 ? 'Unlimited' : plan.max_streaming_slots %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white"><%= plan.max_storage_gb %>GB</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white"><%= plan.subscriber_count || 0 %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex items-center justify-end space-x-2">
                <button onclick="editPlan('<%= plan.id %>')" class="text-primary hover:text-primary-light">
                  <i class="ti ti-edit"></i>
                </button>
                <button onclick="viewSubscribers('<%= plan.id %>')" class="text-blue-400 hover:text-blue-300">
                  <i class="ti ti-users"></i>
                </button>
                <% if (plan.name.toLowerCase() !== 'free' && !plan.name.toLowerCase().includes('free')) { %>
                  <button onclick="deletePlan('<%= plan.id %>')" class="text-red-400 hover:text-red-300">
                    <i class="ti ti-trash"></i>
                  </button>
                <% } %>
              </div>
            </td>
          </tr>
        <% }); %>
      </tbody>
    </table>
  </div>
</div>

<!-- Add/Edit Plan Modal -->
<div id="planModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-2xl">
      <div class="p-6 border-b border-gray-700">
        <div class="flex items-center justify-between">
          <h3 id="modalTitle" class="text-lg font-semibold text-white">Add New Plan</h3>
          <button onclick="closePlanModal()" class="text-gray-400 hover:text-white">
            <i class="ti ti-x text-xl"></i>
          </button>
        </div>
      </div>

      <form id="planForm" class="p-6">
        <input type="hidden" id="planId" name="planId">

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Plan Name</label>
            <input type="text" id="planName" name="name" required
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Price</label>
            <input type="number" id="planPrice" name="price" min="0" step="0.01" required
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <p class="text-xs text-gray-400 mt-1">Use 0 for free plans</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Currency</label>
            <select id="planCurrency" name="currency"
                    class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <option value="USD">USD ($)</option>
              <option value="IDR">IDR (Rp)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Billing Period</label>
            <select id="planBillingPeriod" name="billing_period"
                    class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <option value="monthly">Monthly</option>
              <option value="yearly">Yearly</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Max Streaming Slots</label>
            <input type="number" id="planStreamingSlots" name="max_streaming_slots" min="-1"
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <p class="text-xs text-gray-400 mt-1">Use -1 for unlimited, 0 to disable streaming</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Max Storage</label>
            <div class="flex space-x-2">
              <input type="number" id="planStorageValue" name="max_storage_value" min="0" step="0.001"
                     class="flex-1 bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <select id="planStorageUnit" name="storage_unit"
                      class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
                <option value="gb">GB</option>
                <option value="mb">MB</option>
              </select>
            </div>
            <p class="text-xs text-gray-400 mt-1">Use 0 to disable storage, choose MB for smaller limits (e.g., 500MB)</p>
          </div>
        </div>

        <div class="mt-6">
          <label class="block text-sm font-medium text-gray-300 mb-2">Features</label>
          <div id="featuresContainer" class="space-y-2">
            <div class="flex items-center space-x-2">
              <input type="text" placeholder="Enter feature"
                     class="flex-1 bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <button type="button" onclick="addFeature()" class="bg-primary hover:bg-primary-dark text-white px-3 py-2 rounded-lg">
                <i class="ti ti-plus"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-4 mt-8">
          <button type="button" onclick="closePlanModal()"
                  class="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
            Cancel
          </button>
          <button type="submit"
                  class="px-6 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
            <span id="submitButtonText">Create Plan</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- View Subscribers Modal -->
<div id="subscribersModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-4xl max-h-[80vh] overflow-hidden">
      <div class="p-6 border-b border-gray-700">
        <div class="flex items-center justify-between">
          <h3 id="subscribersModalTitle" class="text-lg font-semibold text-white">Plan Subscribers</h3>
          <button onclick="closeSubscribersModal()" class="text-gray-400 hover:text-white">
            <i class="ti ti-x text-xl"></i>
          </button>
        </div>
      </div>

      <div class="p-6 overflow-y-auto max-h-[60vh]">
        <div id="subscribersContent">
          <div class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p class="text-gray-400 mt-2">Loading subscribers...</p>
          </div>
        </div>
      </div>

      <div class="p-6 border-t border-gray-700">
        <div class="flex justify-end">
          <button onclick="closeSubscribersModal()"
                  class="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  let currentFeatures = [];

  function addNewPlan() {
    document.getElementById('modalTitle').textContent = 'Add New Plan';
    document.getElementById('submitButtonText').textContent = 'Create Plan';
    document.getElementById('planForm').reset();
    document.getElementById('planId').value = '';
    currentFeatures = [];
    updateFeaturesDisplay();
    document.getElementById('planModal').classList.remove('hidden');
  }

  function editPlan(planId) {
    document.getElementById('modalTitle').textContent = 'Edit Plan';
    document.getElementById('submitButtonText').textContent = 'Update Plan';

    // Fetch plan details
    fetch(`/admin/plans/${planId}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const plan = data.plan;
          document.getElementById('planId').value = plan.id;
          document.getElementById('planName').value = plan.name;
          document.getElementById('planPrice').value = plan.price;
          document.getElementById('planCurrency').value = plan.currency;
          document.getElementById('planBillingPeriod').value = plan.billing_period;
          document.getElementById('planStreamingSlots').value = plan.max_streaming_slots;

          // Handle storage display - show in MB if less than 1GB, otherwise GB
          const storageGB = plan.max_storage_gb;
          if (storageGB < 1) {
            document.getElementById('planStorageValue').value = (storageGB * 1024).toFixed(0);
            document.getElementById('planStorageUnit').value = 'mb';
          } else {
            document.getElementById('planStorageValue').value = storageGB;
            document.getElementById('planStorageUnit').value = 'gb';
          }

          currentFeatures = plan.features || [];
          updateFeaturesDisplay();

          document.getElementById('planModal').classList.remove('hidden');
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to load plan details');
      });
  }

  function closePlanModal() {
    document.getElementById('planModal').classList.add('hidden');
  }

  function addFeature() {
    const input = document.querySelector('#featuresContainer input');
    const feature = input.value.trim();

    if (feature && !currentFeatures.includes(feature)) {
      currentFeatures.push(feature);
      input.value = '';
      updateFeaturesDisplay();
    }
  }

  function removeFeature(index) {
    currentFeatures.splice(index, 1);
    updateFeaturesDisplay();
  }

  function updateFeaturesDisplay() {
    const container = document.getElementById('featuresContainer');
    const inputRow = container.querySelector('.flex');

    // Clear existing feature displays
    const existingFeatures = container.querySelectorAll('.feature-item');
    existingFeatures.forEach(item => item.remove());

    // Add current features
    currentFeatures.forEach((feature, index) => {
      const featureDiv = document.createElement('div');
      featureDiv.className = 'feature-item flex items-center justify-between bg-dark-700 px-3 py-2 rounded-lg';
      featureDiv.innerHTML = `
        <span class="text-white">${feature}</span>
        <button type="button" onclick="removeFeature(${index})" class="text-red-400 hover:text-red-300">
          <i class="ti ti-x"></i>
        </button>
      `;
      container.insertBefore(featureDiv, inputRow);
    });
  }

  function deletePlan(planId) {
    if (confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
      fetch(`/admin/plans/${planId}/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          window.location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to delete plan');
      });
    }
  }

  function viewSubscribers(planId) {
    // Show modal
    document.getElementById('subscribersModal').classList.remove('hidden');

    // Reset content
    document.getElementById('subscribersContent').innerHTML = `
      <div class="text-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p class="text-gray-400 mt-2">Loading subscribers...</p>
      </div>
    `;

    // Fetch subscribers
    fetch(`/admin/plans/${planId}/subscribers`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const plan = data.plan;
          const subscribers = data.subscribers;

          document.getElementById('subscribersModalTitle').textContent = `${plan.name} Plan Subscribers`;

          if (subscribers.length === 0) {
            document.getElementById('subscribersContent').innerHTML = `
              <div class="text-center py-8">
                <i class="ti ti-users text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-400">No subscribers found for this plan</p>
              </div>
            `;
          } else {
            let subscribersHtml = `
              <div class="mb-4">
                <p class="text-gray-300">Total subscribers: <span class="text-white font-semibold">${subscribers.length}</span></p>
              </div>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-700">
                  <thead class="bg-gray-700">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Email</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Joined</th>
                      <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-700">
            `;

            subscribers.forEach(subscriber => {
              subscribersHtml += `
                <tr class="hover:bg-dark-700/50 transition-colors">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                        <i class="ti ti-user text-white text-sm"></i>
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-white">${subscriber.username}</div>
                        <div class="text-sm text-gray-400">${subscriber.role}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-white">${subscriber.email || 'No email'}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${subscriber.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                      ${subscriber.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-300">${new Date(subscriber.created_at).toLocaleDateString()}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button onclick="editUser('${subscriber.id}')" class="text-primary hover:text-primary-light mr-2">
                      <i class="ti ti-edit"></i>
                    </button>
                    <button onclick="toggleUserStatus('${subscriber.id}', '${!subscriber.is_active}')" class="text-${subscriber.is_active ? 'red' : 'green'}-400 hover:text-${subscriber.is_active ? 'red' : 'green'}-300">
                      <i class="ti ti-${subscriber.is_active ? 'user-off' : 'user-check'}"></i>
                    </button>
                  </td>
                </tr>
              `;
            });

            subscribersHtml += `
                  </tbody>
                </table>
              </div>
            `;

            document.getElementById('subscribersContent').innerHTML = subscribersHtml;
          }
        } else {
          document.getElementById('subscribersContent').innerHTML = `
            <div class="text-center py-8">
              <i class="ti ti-alert-circle text-red-400 text-4xl mb-4"></i>
              <p class="text-red-400">Error: ${data.error}</p>
            </div>
          `;
        }
      })
      .catch(error => {
        console.error('Error:', error);
        document.getElementById('subscribersContent').innerHTML = `
          <div class="text-center py-8">
            <i class="ti ti-alert-circle text-red-400 text-4xl mb-4"></i>
            <p class="text-red-400">Failed to load subscribers</p>
          </div>
        `;
      });
  }

  function closeSubscribersModal() {
    document.getElementById('subscribersModal').classList.add('hidden');
  }

  // Handle form submission
  document.getElementById('planForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    // Validate required fields
    const nameValue = formData.get('name');
    if (!nameValue || nameValue.trim() === '') {
      alert('Plan name is required');
      return;
    }

    // Trim the name to avoid whitespace issues
    const trimmedName = nameValue.trim();

    // Handle price - allow 0 but not empty/null
    const priceValue = formData.get('price');
    if (priceValue === null || priceValue === '') {
      alert('Price is required (use 0 for free plans)');
      return;
    }
    const price = parseFloat(priceValue);
    if (isNaN(price) || price < 0) {
      alert('Price must be a valid number (0 or greater)');
      return;
    }

    // Handle streaming slots - allow 0 and -1
    const slotsValue = formData.get('max_streaming_slots');
    if (slotsValue === null || slotsValue === '') {
      alert('Max streaming slots is required (use 0 to disable, -1 for unlimited)');
      return;
    }
    const max_streaming_slots = parseInt(slotsValue);
    if (isNaN(max_streaming_slots) || max_streaming_slots < -1) {
      alert('Max streaming slots must be a valid number (-1 for unlimited, 0 or greater)');
      return;
    }

    // Handle storage - allow 0 and convert MB to GB
    const storageValue = formData.get('max_storage_value');
    const storageUnit = formData.get('storage_unit');
    if (storageValue === null || storageValue === '') {
      alert('Max storage is required (use 0 to disable)');
      return;
    }
    let max_storage_gb = parseFloat(storageValue);
    if (isNaN(max_storage_gb) || max_storage_gb < 0) {
      alert('Max storage must be a valid number (0 or greater)');
      return;
    }

    // Convert MB to GB if needed
    if (storageUnit === 'mb') {
      max_storage_gb = max_storage_gb / 1024;
    }

    const planData = {
      name: trimmedName,
      price: price,
      currency: formData.get('currency'),
      billing_period: formData.get('billing_period'),
      max_streaming_slots: max_streaming_slots,
      max_storage_gb: max_storage_gb,
      features: currentFeatures
    };

    const planId = formData.get('planId');
    const url = planId ? `/admin/plans/${planId}/edit` : '/admin/plans/create';

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(planData)
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(data.message || 'Plan saved successfully');
        closePlanModal();
        window.location.reload();
      } else {
        // Show detailed error message if available
        const errorMessage = data.details ? `${data.error}\n\nDetails: ${data.details}` : data.error;
        alert('Error: ' + (errorMessage || 'Failed to save plan'));
        console.error('Plan save error:', data);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Failed to save plan. Please check the console for details.');
    });
  });

  // Allow adding features with Enter key
  document.addEventListener('keydown', function(e) {
    if (e.target.matches('#featuresContainer input') && e.key === 'Enter') {
      e.preventDefault();
      addFeature();
    }
  });

  // Plan bulk selection functionality
  function toggleSelectAllPlans() {
    const selectAll = document.getElementById('selectAllPlans');
    const checkboxes = document.querySelectorAll('.plan-checkbox:not(:disabled)');

    checkboxes.forEach(checkbox => {
      checkbox.checked = selectAll.checked;
    });

    updatePlanBulkActions();
  }

  function updatePlanBulkActions() {
    const checkboxes = document.querySelectorAll('.plan-checkbox:checked');
    const bulkActions = document.getElementById('planBulkActions');
    const selectAll = document.getElementById('selectAllPlans');
    const allCheckboxes = document.querySelectorAll('.plan-checkbox:not(:disabled)');

    // Update select all checkbox state
    if (checkboxes.length === 0) {
      selectAll.indeterminate = false;
      selectAll.checked = false;
    } else if (checkboxes.length === allCheckboxes.length) {
      selectAll.indeterminate = false;
      selectAll.checked = true;
    } else {
      selectAll.indeterminate = true;
    }

    // Show/hide bulk actions
    if (checkboxes.length > 0) {
      bulkActions.classList.remove('hidden');
      bulkActions.classList.add('flex');
    } else {
      bulkActions.classList.add('hidden');
      bulkActions.classList.remove('flex');
    }
  }

  function clearPlanSelection() {
    const checkboxes = document.querySelectorAll('.plan-checkbox');
    const selectAll = document.getElementById('selectAllPlans');

    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
    });
    selectAll.checked = false;
    selectAll.indeterminate = false;

    updatePlanBulkActions();
  }

  function executePlanBulkAction() {
    const action = document.getElementById('planBulkActionSelect').value;
    const selectedPlans = Array.from(document.querySelectorAll('.plan-checkbox:checked')).map(cb => cb.value);

    if (!action) {
      alert('Please select an action');
      return;
    }

    if (selectedPlans.length === 0) {
      alert('Please select at least one plan');
      return;
    }

    let confirmMessage = '';

    switch (action) {
      case 'activate':
        confirmMessage = `Are you sure you want to activate ${selectedPlans.length} plan(s)?`;
        break;
      case 'deactivate':
        confirmMessage = `Are you sure you want to deactivate ${selectedPlans.length} plan(s)?`;
        break;
      case 'delete':
        confirmMessage = `Are you sure you want to delete ${selectedPlans.length} plan(s)? This action cannot be undone.`;
        break;
      default:
        alert('Invalid action');
        return;
    }

    if (confirm(confirmMessage)) {
      fetch('/admin/plans/bulk-action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, planIds: selectedPlans })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const successCount = data.results.filter(r => r.success).length;
          const failCount = data.results.filter(r => !r.success).length;

          let message = `Successfully processed ${successCount} plan(s)`;
          if (failCount > 0) {
            message += `, ${failCount} failed`;
          }

          alert(message);
          window.location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to execute bulk action');
      });
    }
  }
</script>
