<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">User Management</h1>
      <p class="text-gray-400 mt-1">Manage user accounts, roles, and permissions</p>
    </div>
    <div class="flex items-center space-x-4">
      <div id="bulkActions" class="hidden flex items-center space-x-2">
        <select id="bulkActionSelect" class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg">
          <option value="">Select Action</option>
          <option value="activate">Activate Users</option>
          <option value="deactivate">Deactivate Users</option>
          <option value="change_plan">Change Plan</option>
        </select>
        <select id="bulkPlanSelect" class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg hidden">
          <% plans.forEach(function(plan) { %>
            <option value="<%= plan.id %>"><%= plan.name %></option>
          <% }); %>
        </select>
        <button onclick="executeBulkAction()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
          Apply
        </button>
        <button onclick="clearSelection()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
          Clear
        </button>
      </div>
      <button onclick="refreshUsers()" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors">
        <i class="ti ti-refresh mr-2"></i>
        Refresh
      </button>
    </div>
  </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Total Users</p>
        <p class="text-2xl font-bold text-white"><%= stats.total_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-users text-blue-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Active Users</p>
        <p class="text-2xl font-bold text-white"><%= stats.active_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-user-check text-green-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Admins</p>
        <p class="text-2xl font-bold text-white"><%= stats.admin_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-shield text-red-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Premium Users</p>
        <p class="text-2xl font-bold text-white"><%= stats.premium_users || 0 %></p>
      </div>
      <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-crown text-yellow-400 text-xl"></i>
      </div>
    </div>
  </div>
</div>

<!-- Users Table -->
<div class="bg-dark-800 rounded-lg border border-gray-700">
  <div class="p-6 border-b border-gray-700">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-white">All Users</h3>
      <div class="flex items-center space-x-4">
        <div class="relative">
          <input type="text" placeholder="Search users..."
                 class="bg-dark-700 border border-gray-600 text-white pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
        </div>
      </div>
    </div>
  </div>

  <div class="overflow-x-auto">
    <table class="min-w-full">
      <thead class="bg-gray-700">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()"
                   class="rounded border-gray-600 bg-dark-700 text-primary focus:ring-primary">
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Role</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Plan</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Storage</th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-700">
        <% users.forEach(function(user) { %>
          <tr class="hover:bg-dark-700/50 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap">
              <input type="checkbox" class="user-checkbox rounded border-gray-600 bg-dark-700 text-primary focus:ring-primary"
                     value="<%= user.id %>" onchange="updateBulkActions()">
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                  <i class="ti ti-user text-white"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-white"><%= user.username %></div>
                  <div class="text-sm text-gray-400"><%= user.email || 'No email' %></div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.role === 'admin' ? 'bg-red-100 text-red-800' : user.role === 'moderator' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800' %>">
                <%= user.role %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white"><%= user.plan_type %></div>
              <div class="text-sm text-gray-400">
                <%= user.max_streaming_slots === -1 ? 'Unlimited' : user.max_streaming_slots %> slots,
                <%= user.max_storage_gb %>GB
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-white"><%= (user.used_storage_gb || 0).toFixed(2) %>GB used</div>
              <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                <% var percentage = user.max_storage_gb > 0 ? ((user.used_storage_gb || 0) / user.max_storage_gb * 100) : 0; %>
                <div class="bg-primary h-2 rounded-full" style="width: <%= Math.min(percentage, 100) %>%"></div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                <%= user.is_active ? 'Active' : 'Inactive' %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex items-center justify-end space-x-2">
                <button onclick="editUser('<%= user.id %>')" class="text-primary hover:text-primary-light">
                  <i class="ti ti-edit"></i>
                </button>
                <button onclick="toggleUserStatus('<%= user.id %>', <%= user.is_active ? 'false' : 'true' %>)"
                        class="text-<%= user.is_active ? 'red' : 'green' %>-400 hover:text-<%= user.is_active ? 'red' : 'green' %>-300">
                  <i class="ti ti-<%= user.is_active ? 'user-off' : 'user-check' %>"></i>
                </button>
              </div>
            </td>
          </tr>
        <% }); %>
      </tbody>
    </table>
  </div>
</div>

<!-- Edit User Modal -->
<div id="editUserModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-2xl">
      <div class="p-6 border-b border-gray-700">
        <div class="flex items-center justify-between">
          <h3 id="editModalTitle" class="text-lg font-semibold text-white">Edit User</h3>
          <button onclick="closeEditUserModal()" class="text-gray-400 hover:text-white">
            <i class="ti ti-x text-xl"></i>
          </button>
        </div>
      </div>

      <form id="editUserForm" class="p-6">
        <input type="hidden" id="editUserId" name="userId">

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Username</label>
            <input type="text" id="editUsername" name="username" required
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
            <input type="email" id="editEmail" name="email"
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Role</label>
            <select id="editRole" name="role"
                    class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <% roles.forEach(function(role) { %>
                <option value="<%= role.name %>"><%= role.name %></option>
              <% }); %>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Plan</label>
            <select id="editPlan" name="plan"
                    class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <% plans.forEach(function(plan) { %>
                <option value="<%= plan.name %>"><%= plan.name %></option>
              <% }); %>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Max Streaming Slots</label>
            <input type="number" id="editMaxSlots" name="max_streaming_slots" min="-1"
                   class="w-full bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <p class="text-xs text-gray-400 mt-1">Use -1 for unlimited</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Max Storage</label>
            <div class="flex space-x-2">
              <input type="number" id="editMaxStorage" name="max_storage_value" min="1" step="0.001"
                     class="flex-1 bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
              <select id="editStorageUnit" name="storage_unit"
                      class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
                <option value="gb">GB</option>
                <option value="mb">MB</option>
              </select>
            </div>
            <p class="text-xs text-gray-400 mt-1">Choose MB for smaller storage limits (e.g., 500MB)</p>
          </div>

          <div class="md:col-span-2">
            <label class="flex items-center space-x-2">
              <input type="checkbox" id="editIsActive" name="is_active"
                     class="rounded border-gray-600 bg-dark-700 text-primary focus:ring-primary">
              <span class="text-sm text-gray-300">Account Active</span>
            </label>
          </div>
        </div>

        <div class="mt-6">
          <label class="block text-sm font-medium text-gray-300 mb-2">Reset Password</label>
          <div class="flex items-center space-x-4">
            <input type="password" id="editNewPassword" name="new_password" placeholder="Leave empty to keep current password"
                   class="flex-1 bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary">
            <button type="button" onclick="generateRandomPassword()" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg">
              Generate
            </button>
          </div>
        </div>

        <div class="flex justify-end space-x-4 mt-8">
          <button type="button" onclick="closeEditUserModal()"
                  class="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
            Cancel
          </button>
          <button type="submit"
                  class="px-6 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
            Update User
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  function refreshUsers() {
    window.location.reload();
  }

  function editUser(userId) {
    // Fetch user details
    fetch(`/admin/users/${userId}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const user = data.user;

          // Populate form fields
          document.getElementById('editUserId').value = user.id;
          document.getElementById('editUsername').value = user.username;
          document.getElementById('editEmail').value = user.email || '';
          document.getElementById('editRole').value = user.role;
          document.getElementById('editPlan').value = user.plan_type;
          document.getElementById('editMaxSlots').value = user.max_streaming_slots;

          // Handle storage display - show in MB if less than 1GB, otherwise GB
          const storageGB = user.max_storage_gb;
          if (storageGB < 1) {
            document.getElementById('editMaxStorage').value = (storageGB * 1024).toFixed(0);
            document.getElementById('editStorageUnit').value = 'mb';
          } else {
            document.getElementById('editMaxStorage').value = storageGB;
            document.getElementById('editStorageUnit').value = 'gb';
          }

          document.getElementById('editIsActive').checked = user.is_active;
          document.getElementById('editNewPassword').value = '';

          // Show modal
          document.getElementById('editUserModal').classList.remove('hidden');
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to load user details');
      });
  }

  function closeEditUserModal() {
    document.getElementById('editUserModal').classList.add('hidden');
  }

  function generateRandomPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('editNewPassword').value = password;
  }

  function toggleUserStatus(userId, newStatus) {
    if (confirm(`Are you sure you want to ${newStatus === 'true' ? 'activate' : 'deactivate'} this user?`)) {
      fetch('/admin/users/toggle-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, isActive: newStatus === 'true' })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          window.location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to update user status');
      });
    }
  }

  // Bulk selection functionality
  function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');

    checkboxes.forEach(checkbox => {
      checkbox.checked = selectAll.checked;
    });

    updateBulkActions();
  }

  function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    const bulkActions = document.getElementById('bulkActions');
    const selectAll = document.getElementById('selectAll');
    const allCheckboxes = document.querySelectorAll('.user-checkbox');

    // Update select all checkbox state
    if (checkboxes.length === 0) {
      selectAll.indeterminate = false;
      selectAll.checked = false;
    } else if (checkboxes.length === allCheckboxes.length) {
      selectAll.indeterminate = false;
      selectAll.checked = true;
    } else {
      selectAll.indeterminate = true;
    }

    // Show/hide bulk actions
    if (checkboxes.length > 0) {
      bulkActions.classList.remove('hidden');
      bulkActions.classList.add('flex');
    } else {
      bulkActions.classList.add('hidden');
      bulkActions.classList.remove('flex');
    }
  }

  function clearSelection() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    const selectAll = document.getElementById('selectAll');

    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
    });
    selectAll.checked = false;
    selectAll.indeterminate = false;

    updateBulkActions();
  }

  function executeBulkAction() {
    const action = document.getElementById('bulkActionSelect').value;
    const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);

    if (!action) {
      alert('Please select an action');
      return;
    }

    if (selectedUsers.length === 0) {
      alert('Please select at least one user');
      return;
    }

    let confirmMessage = '';
    let requestData = { action, userIds: selectedUsers };

    switch (action) {
      case 'activate':
        confirmMessage = `Are you sure you want to activate ${selectedUsers.length} user(s)?`;
        break;
      case 'deactivate':
        confirmMessage = `Are you sure you want to deactivate ${selectedUsers.length} user(s)?`;
        break;
      case 'change_plan':
        const planId = document.getElementById('bulkPlanSelect').value;
        if (!planId) {
          alert('Please select a plan');
          return;
        }
        requestData.planId = planId;
        const planName = document.getElementById('bulkPlanSelect').selectedOptions[0].text;
        confirmMessage = `Are you sure you want to change ${selectedUsers.length} user(s) to the ${planName} plan?`;
        break;
      default:
        alert('Invalid action');
        return;
    }

    if (confirm(confirmMessage)) {
      fetch('/admin/users/bulk-action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const successCount = data.results.filter(r => r.success).length;
          const failCount = data.results.filter(r => !r.success).length;

          let message = `Successfully processed ${successCount} user(s)`;
          if (failCount > 0) {
            message += `, ${failCount} failed`;
          }

          alert(message);
          window.location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to execute bulk action');
      });
    }
  }

  // Show/hide plan select based on action
  document.getElementById('bulkActionSelect').addEventListener('change', function() {
    const planSelect = document.getElementById('bulkPlanSelect');
    if (this.value === 'change_plan') {
      planSelect.classList.remove('hidden');
    } else {
      planSelect.classList.add('hidden');
    }
  });

  // Handle edit user form submission
  document.getElementById('editUserForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    // Handle storage conversion
    const storageValue = parseFloat(formData.get('max_storage_value'));
    const storageUnit = formData.get('storage_unit');
    let storageGB = storageValue;

    if (storageUnit === 'mb') {
      storageGB = storageValue / 1024; // Convert MB to GB
    }

    const userData = {
      userId: formData.get('userId'),
      username: formData.get('username'),
      email: formData.get('email'),
      role: formData.get('role'),
      plan: formData.get('plan'),
      max_streaming_slots: parseInt(formData.get('max_streaming_slots')),
      max_storage_gb: storageGB,
      is_active: formData.has('is_active')
    };

    // Only include password if it's provided
    const newPassword = formData.get('new_password');
    if (newPassword && newPassword.trim()) {
      userData.new_password = newPassword.trim();
    }

    fetch('/admin/users/edit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        closeEditUserModal();
        window.location.reload();
      } else {
        alert('Error: ' + data.error);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Failed to update user');
    });
  });
</script>
